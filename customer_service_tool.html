<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Adventure Entertainment - Customer Service Tool</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .main-content {
            padding: 30px;
        }
        
        .search-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        
        .search-box {
            width: 100%;
            padding: 15px;
            font-size: 16px;
            border: 2px solid #ddd;
            border-radius: 8px;
            margin-bottom: 15px;
        }
        
        .search-box:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .quick-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .quick-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .quick-btn:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        
        .calculator-section {
            background: #e8f5e8;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        
        .calc-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .calc-input {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .calc-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
        }
        
        .calc-btn:hover {
            background: #218838;
        }
        
        .result-section {
            background: #fff3cd;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
            border-left: 4px solid #ffc107;
        }
        
        .response-area {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            min-height: 200px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        
        .copy-btn {
            background: #17a2b8;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 10px;
        }
        
        .copy-btn:hover {
            background: #138496;
        }
        
        .tabs {
            display: flex;
            background: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .tab {
            flex: 1;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .tab.active {
            background: #667eea;
            color: white;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎮 Customer Service Tool</h1>
            <p>Adventure Entertainment Singapore - Quick Response System</p>
        </div>
        
        <div class="main-content">
            <div class="tabs">
                <div class="tab active" onclick="showTab('search')">🔍 Find Response</div>
                <div class="tab" onclick="showTab('calculator')">💰 Price Calculator</div>
                <div class="tab" onclick="showTab('templates')">📝 Quick Templates</div>
            </div>
            
            <!-- SEARCH TAB -->
            <div id="search" class="tab-content active">
                <div class="search-section">
                    <h3>🔍 Find the Right Response</h3>
                    <input type="text" class="search-box" id="searchInput" placeholder="Type customer question or keywords (e.g., 'pricing', 'group discount', 'cancellation')">
                    
                    <div class="quick-buttons">
                        <button class="quick-btn" onclick="showResponse('pricing')">Basic Pricing</button>
                        <button class="quick-btn" onclick="showResponse('group-discount')">Group Discounts</button>
                        <button class="quick-btn" onclick="showResponse('booking')">How to Book</button>
                        <button class="quick-btn" onclick="showResponse('cancellation')">Cancellation Policy</button>
                        <button class="quick-btn" onclick="showResponse('games')">Game Details</button>
                        <button class="quick-btn" onclick="showResponse('location')">Location & Hours</button>
                    </div>
                </div>
                
                <div class="response-area" id="responseArea">
                    Click a button above or search for keywords to get ready-to-send responses!
                </div>
                
                <button class="copy-btn" onclick="copyResponse()">📋 Copy Response</button>
                <div class="success-message" id="copySuccess">✅ Response copied to clipboard!</div>
            </div>
            
            <!-- CALCULATOR TAB -->
            <div id="calculator" class="tab-content">
                <div class="calculator-section">
                    <h3>💰 Quick Price Calculator</h3>
                    
                    <div class="calc-grid">
                        <select class="calc-input" id="outlet">
                            <option value="">Select Outlet</option>
                            <option value="triggered">Triggered Games</option>
                            <option value="trapped">Trapped Escape Room</option>
                        </select>
                        
                        <input type="number" class="calc-input" id="participants" placeholder="Number of participants" min="1">
                        
                        <select class="calc-input" id="timing">
                            <option value="">Select Timing</option>
                            <option value="off-peak">Off-peak</option>
                            <option value="peak">Peak</option>
                        </select>
                        
                        <input type="number" class="calc-input" id="games" placeholder="Games per person" min="1" max="5">

                        <label style="display: flex; align-items: center; gap: 10px;">
                            <input type="checkbox" id="mysteryHunt"> Add Mystery Hunt (+$20/person)
                        </label>

                        <label style="display: flex; align-items: center; gap: 10px;">
                            <input type="checkbox" id="earlyBird"> Early Bird (Trapped only, -$3/game)
                        </label>
                    </div>

                    <button class="calc-btn" onclick="calculatePrice()">Calculate Quote</button>
                    
                    <div class="result-section" id="calcResult" style="display: none;">
                        <h4>💰 Your Quote:</h4>
                        <div id="quoteDetails"></div>
                        <button class="copy-btn" onclick="copyQuote()">📋 Copy Quote</button>
                    </div>
                </div>
            </div>
            
            <!-- TEMPLATES TAB -->
            <div id="templates" class="tab-content">
                <div class="search-section">
                    <h3>📝 Quick Templates</h3>
                    <p>One-click responses for common situations:</p>
                    
                    <div class="quick-buttons">
                        <button class="quick-btn" onclick="showTemplate('availability')">Check Availability</button>
                        <button class="quick-btn" onclick="showTemplate('payment-reminder')">Payment Reminder</button>
                        <button class="quick-btn" onclick="showTemplate('booking-confirmed')">Booking Confirmed</button>
                        <button class="quick-btn" onclick="showTemplate('reschedule')">Reschedule Request</button>
                        <button class="quick-btn" onclick="showTemplate('pregnant-guest')">Pregnant Guest</button>
                        <button class="quick-btn" onclick="showTemplate('large-group')">Large Group (40+)</button>
                    </div>
                </div>
                
                <div class="response-area" id="templateArea">
                    Select a template above to get instant responses!
                </div>
                
                <button class="copy-btn" onclick="copyTemplate()">📋 Copy Template</button>
                <div class="success-message" id="templateCopySuccess">✅ Template copied to clipboard!</div>
            </div>
        </div>
    </div>

    <script>
        // Tab switching
        function showTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // Remove active class from all tabs
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Show selected tab content
            document.getElementById(tabName).classList.add('active');
            
            // Add active class to clicked tab
            event.target.classList.add('active');
        }
        
        // Response templates
        const responses = {
            'pricing': `Hi [Customer Name],

Thank you for your interest! Here's our pricing structure:

🏠 **TRAPPED ESCAPE ROOM (75 mins each)**
Off-peak (Mon-Fri before 6pm): $28.90 per game
Peak (Evenings/Weekends): $34.90 per game
*Prices exclude GST

🎮 **TRIGGERED GAMES (20 mins each)**
Off-peak (Mon-Thu all day, Fri before 6pm): $16.00 per game
Peak (Fri after 6pm, weekends): $20.00 per game
*Prices include GST

To provide you with an accurate quote, could you please let me know:
1. Number of participants
2. Preferred date and time
3. Which outlet interests you (Trapped or Triggered)
4. How many games per person

I'll calculate your total cost including any applicable discounts!

Best regards,
[Your Name]
Adventure Entertainment Singapore`,

            'group-discount': `Hi [Customer Name],

Yes, we offer attractive group discounts! Here's the breakdown:

🏠 **TRAPPED ESCAPE ROOM:**
- 1-19 pax: Standard rate
- 20-39 pax: $1 discount per game per person
- 40+ pax: $2 discount per game per person

🎮 **TRIGGERED GAMES:**
- 1-19 pax: Standard rate
- 20-39 pax: 10% discount per game per person
- 40+ pax: 15% discount per game per person

💰 **ADDITIONAL SAVINGS:**
- Early bird discount (Trapped only): Extra $3 off per game if deposit paid 3 months in advance

Would you like me to calculate the exact savings for your group size?

Best regards,
[Your Name]
Adventure Entertainment Singapore`,

            'booking': `Hi [Customer Name],

Here's our simple booking process:

📋 **STEP-BY-STEP BOOKING:**
1. Choose your outlet: Trapped or Triggered
2. Select participants: How many people
3. Pick your package: Number of games per person
4. Choose date & time: We'll confirm availability
5. Receive quote: Total pricing with any discounts
6. Pay deposit: 50% to secure your slot
7. Final payment: Balance on event day

⏰ **IMPORTANT TIMING:**
- Confirm booking within 3 working days
- Bookings are first-come, first-served

Ready to start your booking? Just let me know your preferred details!

Best regards,
[Your Name]
Adventure Entertainment Singapore`,

            'cancellation': `Hi [Customer Name],

Here's our cancellation policy:

❌ **CANCELLATION TERMS:**
- All deposits are strictly non-refundable
- No exceptions for any reason (weather, illness, etc.)

🔄 **ALTERNATIVE OPTIONS:**
- One-time rescheduling available (subject to availability)
- Must notify us at least 1 week in advance
- Only one rescheduling permitted per booking

⚠️ **IMPORTANT NOTES:**
- Changes less than 1 week before event = deposit forfeiture
- No-shows will still be charged full amount

💡 **OUR RECOMMENDATION:**
Consider rescheduling instead of cancelling if your plans change.

Is there anything else I can help clarify?

Best regards,
[Your Name]
Adventure Entertainment Singapore`,

            'games': `Hi [Customer Name],

Here are our exciting game options:

🏠 **TRAPPED ESCAPE ROOM (75 mins each):**
- The Cursed Video Tape (Thriller with live actor)
- The Strangest Things (Sci-fi mystery)
- The Great Escape (Prison theme, witty & fun)
- Hostel Warlock (Supernatural investigation)
- The Attic (Kidnapped thriller)
- The Asylum (Investigative mystery)
- The Alchemist's Cabinet (Magic theme, NO scary elements)

🎮 **TRIGGERED GAMES (20 mins each):**
- Floor is Lava (2 rooms) - Navigate safe zones
- Press It! - Memory challenge with buttons
- Hoops Madness - Basketball with a twist
- Hide & Seek - Avoid the devil's eye
- Hexa Blasts - Hit hexagon patterns

Which themes interest your group most?

Best regards,
[Your Name]
Adventure Entertainment Singapore`,

            'location': `Hi [Customer Name],

Here are our location details:

📍 **ADDRESS:**
8 Grange Road, #02-08
Cathay Cineleisure Orchard
Singapore 239695

🕐 **OPERATING HOURS:**
Daily from 11am - 10pm

🚗 **GETTING THERE:**
- MRT: Orchard Station (5-min walk)
- Ample parking available at Cathay Cineleisure
- Central location with restaurants and amenities nearby

Both Trapped and Triggered are located in the same building!

Let me know if you need any other information.

Best regards,
[Your Name]
Adventure Entertainment Singapore`
        };
        
        function showResponse(type) {
            document.getElementById('responseArea').textContent = responses[type] || 'Response not found.';
        }
        
        function copyResponse() {
            const responseText = document.getElementById('responseArea').textContent;
            navigator.clipboard.writeText(responseText).then(() => {
                document.getElementById('copySuccess').style.display = 'block';
                setTimeout(() => {
                    document.getElementById('copySuccess').style.display = 'none';
                }, 3000);
            });
        }
        
        // Price calculator
        function calculatePrice() {
            const outlet = document.getElementById('outlet').value;
            const participants = parseInt(document.getElementById('participants').value);
            const timing = document.getElementById('timing').value;
            const games = parseInt(document.getElementById('games').value);
            const mysteryHunt = document.getElementById('mysteryHunt').checked;
            const earlyBird = document.getElementById('earlyBird').checked;

            if (!outlet || !participants || !timing || !games) {
                alert('Please fill in all fields');
                return;
            }

            let basePrice = 0;
            let gstNote = '';

            if (outlet === 'triggered') {
                basePrice = timing === 'off-peak' ? 16 : 20;
                gstNote = '(GST included)';
            } else {
                basePrice = timing === 'off-peak' ? 28.90 : 34.90;
                gstNote = '(GST not included)';
            }

            let totalBase = participants * basePrice * games;
            let discount = 0;
            let discountText = '';
            let additionalCosts = 0;
            let additionalText = '';

            // Apply group discounts
            if (participants >= 40) {
                if (outlet === 'triggered') {
                    discount = totalBase * 0.15;
                    discountText = '15% group discount';
                } else {
                    discount = participants * games * 2;
                    discountText = '$2 per game group discount';
                }
            } else if (participants >= 20) {
                if (outlet === 'triggered') {
                    discount = totalBase * 0.10;
                    discountText = '10% group discount';
                } else {
                    discount = participants * games * 1;
                    discountText = '$1 per game group discount';
                }
            }

            // Apply early bird discount (Trapped only)
            if (earlyBird && outlet === 'trapped') {
                discount += participants * games * 3;
                discountText += (discountText ? ' + ' : '') + 'Early bird discount';
            }

            // Add Mystery Hunt
            if (mysteryHunt) {
                additionalCosts = participants * 20;
                additionalText = `Mystery Hunt: ${participants} × $20 = $${additionalCosts}`;
            }

            let finalPrice = totalBase - discount + additionalCosts;
            let totalSavings = discount;

            const quoteHtml = `
                <strong>${outlet.toUpperCase()} - ${timing.toUpperCase()}</strong><br>
                ${participants} participants × ${games} games × $${basePrice} = $${totalBase.toFixed(2)}<br>
                ${discount > 0 ? `${discountText}: -$${discount.toFixed(2)}<br>` : ''}
                ${additionalText ? `${additionalText}<br>` : ''}
                <strong>TOTAL: $${finalPrice.toFixed(2)} ${gstNote}</strong><br><br>
                ${totalSavings > 0 ? `<em>You save: $${totalSavings.toFixed(2)}!</em><br>` : ''}
                <em>Deposit required: $${(finalPrice * 0.5).toFixed(2)} (50%)</em>
            `;

            document.getElementById('quoteDetails').innerHTML = quoteHtml;
            document.getElementById('calcResult').style.display = 'block';
        }
        
        function copyQuote() {
            const quoteText = document.getElementById('quoteDetails').textContent;
            navigator.clipboard.writeText(quoteText);
        }

        // Template responses
        const templates = {
            'availability': `Hi [Customer Name],

Let me check availability for [date].

Could you please confirm:
1. How many participants?
2. Preferred time slot?
3. Trapped or Triggered games?

I'll get back to you within 2 hours with available slots!

Best regards,
[Your Name]
Adventure Entertainment Singapore`,

            'payment-reminder': `Hi [Customer Name],

Friendly reminder: Your booking deposit is due by [date] to secure your slot.

Payment details:
[Include payment info]

Your slot is temporarily held until the deadline. Let me know if you need any assistance!

Best regards,
[Your Name]
Adventure Entertainment Singapore`,

            'booking-confirmed': `🎉 BOOKING CONFIRMED! 🎉

Hi [Customer Name],

Date: [Date]
Time: [Time]
Participants: [Number]
Total: $[Amount]

Next steps:
- Organize team groupings in advance
- Wear covered footwear (no sandals/heels)
- Arrive 10-15 minutes early

We're excited to see you soon!

Best regards,
[Your Name]
Adventure Entertainment Singapore`,

            'reschedule': `Hi [Customer Name],

Yes, we offer one-time rescheduling if you notify us at least 1 week in advance.

Please provide 2-3 alternative dates and I'll check availability.
What new dates work better for your group?

Note: Only one rescheduling permitted per booking.

Best regards,
[Your Name]
Adventure Entertainment Singapore`,

            'pregnant-guest': `Hi [Customer Name],

For safety reasons, our Triggered Games are not suitable for pregnant guests due to physical activity requirements.

However, our Trapped Escape Room activities are perfect alternatives - they focus on mental challenges rather than physical activity.

Would you like information about our escape room options instead?

Best regards,
[Your Name]
Adventure Entertainment Singapore`,

            'large-group': `Hi [Customer Name],

For 40+ participants, I recommend combining:
- Triggered Games (max 46 capacity)
- Mystery Hunt add-on ($20/person)

This allows half your group to start with games while the other half does Mystery Hunt, then swap!

Perfect solution for maximum engagement. Interested in this combination?

Best regards,
[Your Name]
Adventure Entertainment Singapore`
        };

        function showTemplate(type) {
            document.getElementById('templateArea').textContent = templates[type] || 'Template not found.';
        }

        function copyTemplate() {
            const templateText = document.getElementById('templateArea').textContent;
            navigator.clipboard.writeText(templateText).then(() => {
                document.getElementById('templateCopySuccess').style.display = 'block';
                setTimeout(() => {
                    document.getElementById('templateCopySuccess').style.display = 'none';
                }, 3000);
            });
        }
        
        // Search functionality
        document.getElementById('searchInput').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            
            if (searchTerm.includes('price') || searchTerm.includes('cost')) {
                showResponse('pricing');
            } else if (searchTerm.includes('group') || searchTerm.includes('discount')) {
                showResponse('group-discount');
            } else if (searchTerm.includes('book') || searchTerm.includes('reserve')) {
                showResponse('booking');
            }
        });
    </script>
</body>
</html>
