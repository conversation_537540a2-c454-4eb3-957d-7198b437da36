<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Triggered Games - Q&A System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #e67e22, #d35400);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .controls {
            padding: 20px 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            align-items: center;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #e67e22;
            color: white;
        }
        
        .btn-primary:hover {
            background: #d35400;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #1e7e34;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-warning:hover {
            background: #e0a800;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .main-content {
            padding: 30px;
        }
        
        .qa-item {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            margin-bottom: 20px;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .qa-item:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .qa-header {
            background: #e9ecef;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
        }
        
        .qa-header:hover {
            background: #dee2e6;
        }
        
        .question {
            font-weight: 600;
            color: #495057;
            flex: 1;
        }
        
        .qa-controls {
            display: flex;
            gap: 10px;
        }
        
        .btn-sm {
            padding: 5px 10px;
            font-size: 0.875em;
        }
        
        .qa-content {
            padding: 20px;
            display: none;
        }
        
        .qa-content.active {
            display: block;
        }
        
        .answer-display {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 15px;
            white-space: pre-wrap;
            line-height: 1.6;
        }
        
        .answer-edit {
            width: 100%;
            min-height: 150px;
            padding: 15px;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            font-family: inherit;
            font-size: 14px;
            line-height: 1.6;
            resize: vertical;
        }
        
        .edit-controls {
            margin-top: 10px;
            display: flex;
            gap: 10px;
        }
        
        .category-filter {
            margin-bottom: 20px;
        }
        
        .category-filter select {
            padding: 8px 12px;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .stats {
            background: #fff5f5;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: flex;
            gap: 30px;
            flex-wrap: wrap;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 1.5em;
            font-weight: bold;
            color: #e67e22;
        }
        
        .stat-label {
            font-size: 0.875em;
            color: #666;
        }
        
        .search-box {
            width: 300px;
            padding: 8px 12px;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .export-section {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
        }
        
        .client-processor-section {
            background: #e8f5e8;
            border: 1px solid #b8e6b8;
            border-radius: 5px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .result-item {
            background: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .result-question {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
            padding: 8px;
            background: #f8f9fa;
            border-left: 4px solid #e67e22;
        }
        
        .result-answer {
            background: #f8f9fa;
            padding: 12px;
            border-radius: 4px;
            margin-bottom: 10px;
            white-space: pre-wrap;
            font-family: inherit;
        }
        
        .copy-answer-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.3s ease;
        }
        
        .copy-answer-btn:hover {
            background: #1e7e34;
        }
        
        .no-match {
            color: #dc3545;
            font-style: italic;
            padding: 10px;
            background: #f8d7da;
            border-radius: 4px;
        }
        
        .hidden {
            display: none;
        }
        
        .collapse-icon {
            transition: transform 0.3s ease;
        }
        
        .collapse-icon.rotated {
            transform: rotate(180deg);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎮 Triggered Games Q&A</h1>
            <p>20-minute interactive physical games | High-energy team challenges</p>
        </div>
        
        <div class="controls">
            <button class="btn btn-primary" onclick="generateNewQA()">🔄 Generate New Q&A</button>
            <button class="btn btn-success" onclick="addCustomQA()">➕ Add Custom Q&A</button>
            <button class="btn btn-warning" onclick="exportQA()">📤 Export All Q&A</button>
            <button class="btn btn-success" onclick="toggleClientProcessor()">📝 Client Q&A Processor</button>
            <input type="text" class="search-box" placeholder="Search questions..." onkeyup="searchQA(this.value)">
            <div class="category-filter">
                <select onchange="filterByCategory(this.value)">
                    <option value="">All Categories</option>
                    <option value="general">General Info</option>
                    <option value="pricing">Pricing</option>
                    <option value="booking">Booking</option>
                    <option value="games">Games</option>
                    <option value="policies">Policies</option>
                </select>
            </div>
        </div>
        
        <div class="main-content">
            <div class="stats">
                <div class="stat-item">
                    <div class="stat-number" id="totalQA">0</div>
                    <div class="stat-label">Total Q&A</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="editedQA">0</div>
                    <div class="stat-label">Edited</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="categoriesCount">0</div>
                    <div class="stat-label">Categories</div>
                </div>
            </div>
            
            <div id="qaContainer">
                <!-- Q&A items will be dynamically generated here -->
            </div>
            
            <div class="export-section hidden" id="exportSection">
                <h4>📋 Export Data</h4>
                <textarea id="exportData" readonly style="width: 100%; height: 200px; margin-top: 10px;"></textarea>
                <div style="margin-top: 10px;">
                    <button class="btn btn-primary" onclick="copyToClipboard()">📋 Copy to Clipboard</button>
                    <button class="btn btn-secondary" onclick="downloadJSON()">💾 Download JSON</button>
                </div>
            </div>
            
            <div class="client-processor-section hidden" id="clientProcessorSection">
                <h4>📝 Client Question Processor</h4>
                <p style="margin-bottom: 15px; color: #666;">Paste multiple client questions below. The system will search for keywords and provide matching answers.</p>
                
                <div style="margin-bottom: 20px;">
                    <label for="clientQuestions" style="display: block; margin-bottom: 5px; font-weight: bold;">Client Questions:</label>
                    <textarea id="clientQuestions" placeholder="Paste your client questions here...\n\nExample:\n1. How much for 25 people?\n2. What games do you have?\n3. Can we book for next weekend?" style="width: 100%; height: 150px; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-family: inherit;"></textarea>
                </div>
                
                <div style="margin-bottom: 20px;">
                    <button class="btn btn-primary" onclick="processClientQuestions()">🔍 Process Questions</button>
                    <button class="btn btn-secondary" onclick="clearClientProcessor()">🗑️ Clear All</button>
                </div>
                
                <div id="processedResults" style="display: none;">
                    <h5 style="margin-bottom: 15px;">🎯 Processed Results:</h5>
                    <div id="resultsContainer"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let qaData = [];
        let editedAnswers = new Set();
        
        // Triggered Games specific Q&A
        const triggeredQAData = [
            {
                id: 1,
                category: 'general',
                question: 'What is Triggered Games?',
                answer: `🎮 **TRIGGERED GAMES**

We offer high-energy interactive physical challenge games where teams compete in fast-paced, adrenaline-pumping activities.

**KEY FEATURES:**
- 20-minute interactive physical games
- 5 high-energy challenge rooms
- Physical activity combined with strategy
- Perfect for team building and active groups
- Maximum 46 participants across all rooms

**LOCATION:**
8 Grange Road, #02-08
Cathay Cineleisure Orchard
Singapore 239695

**OPERATING HOURS:**
Daily from 11am - 10pm

**FOCUS:** Physical challenges, teamwork, and competitive fun!`
            },
            {
                id: 2,
                category: 'games',
                question: 'What games do you have at Triggered Games?',
                answer: `🎮 **OUR 5 EXCITING GAMES (20 minutes each):**

🌋 **1. FLOOR IS LAVA (Rooms 1 & 2)** - Max 10 people each
In this game, only the green zones are safe—step into the red lava zones, and you'll lose points! Navigate through a series of challenges by tapping the blue grids to complete each level. Avoid the lava and keep your points intact!

🔘 **2. PRESS IT! (Room 3)** - Max 8 people
A line of buttons stretches across the walls, and your memory is your best asset. Memorize the pattern of lights, and press the correct buttons in the right order. Hit the wrong button, and you'll lose valuable points. Can you remember the pattern?

🏀 **3. HOOPS MADNESS (Room 4)** - Max 10 people
Get ready to throw balls into illuminated hoops, but there's a twist—only the hoops glowing with a certain light count towards your score. If you aim for a red-lit hoop, points will be deducted. Precision is key in this fast-paced game!

🔶 **4. HEXA BLASTS (Room 4)** - Max 10 people
Hexagon-shaped lights will flash on the wall in different patterns, and your goal is to hit the right hexagons using the balls provided. The challenge? Hitting the wrong hexagon will cost you points. Sharpen your aim and strategy to rack up points!

👁️ **5. HIDE & SEEK (Room 5)** - Max 8 people
Complete your mission while avoiding the devil's eye in this thrilling game! There's a pillar in the center of the room where you can hide, but if the eye spots you, points are deducted. Stay hidden while completing your tasks for the ultimate victory!

**Duration:** 20 minutes each game
**Total Rooms:** 5 game rooms (Hoops Madness & Hexa Blasts share Room 4)
**Total Capacity:** 46 participants across all rooms
**Focus:** Physical activity, strategy, memory, and team competition`
            }
        ];

        // Additional Triggered-specific Q&A
        const moreTriggeredQA = [
            {
                id: 3,
                category: 'pricing',
                question: 'What are your Triggered Games pricing and packages?',
                answer: `🎮 **TRIGGERED GAMES CORPORATE PRICING (20 mins each)**

🔥 **SPECIAL CORPORATE LAUNCH OFFER: Up to 15% OFF!** 🔥
*The Adult rate is now on promotion and is the same as the student rate*

**OFF-PEAK (Mon-Thu all day, Fri before 6pm):**
• 1-19 people: $16.00 per game per person
• 20-39 people: $14.40 per game per person (10% discount)
• 40+ people: $13.60 per game per person (15% discount)

**PEAK (Fri after 6pm, weekends & holidays):**
• 1-19 people: $20.00 per game per person
• 20-39 people: $18.00 per game per person (10% discount)
• 40+ people: $17.00 per game per person (15% discount)

*All prices are NETT (include GST)*

📦 **EXCITING PACKAGES AVAILABLE:**
Choose from three dynamic packages, designed to challenge and engage your team:
• **Package 1:** 1 x 20mins game per person
• **Package 2:** 2 x 20mins games per person (back to back adventures)
• **Package 3:** 3 x 20mins games per person

**EXAMPLE SAVINGS (38 pax, off-peak):**
• 1 game: 38 × $14.40 (up from $16.00) = $547.20 (Savings of $60.80!)
• 2 games: 76 × $13.60 (up from $16.00) = $1,033.60 (Savings of $182.40!)

**CAPACITY:** Maximum 46 participants across all game rooms

🎯 **MYSTERY HUNT ADD-ON:** Available for full experience (contact for pricing)`
            },
            {
                id: 4,
                category: 'pricing',
                question: 'Do you offer group discounts for Triggered Games?',
                answer: `Yes! We offer excellent group discounts for Triggered Games:

🎮 **TRIGGERED GAMES GROUP DISCOUNTS:**
- 1-19 pax: Standard rate
- 20-39 pax: 10% discount per game per person
- 40+ pax: 15% discount per game per person

**EXAMPLE SAVINGS (25 people, 2 games, off-peak):**
Regular rate: 25 × $16.00 × 2 = $800
Group discount: 25 × $14.40 × 2 = $720
**You save: $80!**

**EXAMPLE SAVINGS (50 people, 3 games, peak):**
Regular rate: 50 × $20.00 × 3 = $3,000
Group discount: 50 × $17.00 × 3 = $2,550
**You save: $450!**

*All prices include GST*
*Note: Early bird discounts are NOT available for Triggered Games*`
            },
            {
                id: 5,
                category: 'booking',
                question: 'How do I book Triggered Games sessions?',
                answer: `📋 **TRIGGERED GAMES BOOKING PROCESS:**

1. **Choose your games:** Select from our 5 high-energy rooms
2. **Select participants:** Maximum 46 people total
3. **Pick your package:** 1-5 games per person
4. **Choose date & time:** We'll confirm availability
5. **Add Mystery Hunt:** Optional $20/person add-on
6. **Receive quote:** Total pricing with discounts
7. **Pay deposit:** 50% to secure your slot
8. **Final payment:** Balance on event day

💳 **PAYMENT METHODS:**
- Bank transfer, Credit card (Visa, Master, Amex)
- PayNow UEN, Cash (on event day)
- VENDORS.GOV.SG / GEBIZ (for government bookings)

⏰ **IMPORTANT TIMING:**
- Confirm booking within 3 working days
- Bookings are first-come, first-served
- We recommend booking 2-3 weeks in advance

**TEAM GROUPING:**
- Organize team groupings before arrival
- No holding areas available (all spaces are game rooms)
- Facilitators will gather pre-assigned groups upon arrival`
            },
            {
                id: 6,
                category: 'general',
                question: 'Are there age restrictions or accessibility concerns for Triggered Games?',
                answer: `👥 **AGE REQUIREMENTS:**
- No specific age restrictions
- Children should be accompanied by adults
- Games designed for teens and adults
- Requires physical activity and mobility

♿ **ACCESSIBILITY & SAFETY:**
- Unfortunately, NOT wheelchair accessible
- Games involve running, jumping, and reaching high places
- NOT suitable for pregnant guests at any stage due to physical activity requirements
- Requires good mobility and physical fitness

🏥 **ALTERNATIVE FOR MOBILITY CONCERNS:**
For pregnant guests or those with mobility issues, we highly recommend our Trapped Escape Room activities instead, as they focus on mental challenges rather than physical activity.

👕 **PHYSICAL REQUIREMENTS:**
- Covered footwear mandatory (no sandals, heels, crocs, slippers)
- Comfortable clothing for physical movement
- Ability to run, jump, and reach high places`
            },
            {
                id: 7,
                category: 'policies',
                question: 'What is your cancellation and rescheduling policy for Triggered Games?',
                answer: `📋 **CANCELLATION & RESCHEDULING POLICY:**

We understand that plans can change, so we offer a one-time rescheduling option for your team bonding event, subject to availability.

🔄 **RESCHEDULING OPTIONS:**
- **One-time rescheduling** available (subject to availability)
- Must inform us at least 1 week in advance
- No changes allowed if notified less than 1 week before event
- You can only reschedule once
- Any further cancellations or changes = deposit forfeiture

❌ **CANCELLATION POLICY:**
- All deposits are strictly non-refundable
- No exceptions regardless of cancellation notice period or reason
- This policy helps us maintain fair pricing and availability

⚠️ **IMPORTANT NOTES:**
- Less than 1 week notice = no rescheduling allowed
- Second rescheduling attempt = deposit forfeiture
- Peak to off-peak moves may require price adjustment

**CAPACITY LIMITS:**
- Maximum 46 participants total across all rooms
- Individual room capacities must be respected`
            },
            {
                id: 8,
                category: 'games',
                question: 'What are the team building benefits of Triggered Games?',
                answer: `🏆 **TEAM BUILDING BENEFITS:**

**COLLABORATION & COMMUNICATION:**
- Teams work together to navigate challenges
- Requires quick decision-making and coordination
- Enhances verbal and non-verbal communication
- Builds trust through shared physical challenges

**STRATEGIC THINKING:**
- Combines physical activity with mental strategy
- Problem-solving under time pressure
- Resource allocation and task delegation
- Adaptability to changing game conditions

**COMPETITIVE ELEMENT:**
- Teams compete for highest scores
- Motivates performance and engagement
- Creates memorable shared experiences
- Builds healthy competition spirit

**PHYSICAL ENGAGEMENT:**
- High-energy, immersive experience
- Engages all team members simultaneously
- Breaks down barriers through shared activity
- Boosts energy and morale

**FACILITATOR SUPPORT:**
- Guided experience led by energetic facilitators
- Professional guidance throughout games
- Winner announcements and group photos
- Ensures fair play and maximum engagement`
            },
            {
                id: 9,
                category: 'booking',
                question: 'What information do you need for booking and what is the booking process?',
                answer: `📋 **INFORMATION NEEDED FOR BOOKING:**
- Contact person name & mobile number
- Organization name & billing address
- 1st and 2nd contact persons with mobile numbers
- Number of participants (maximum 46 total)
- Preferred date and time
- Package preference (1-3 games per person)
- Duration preference (1 hour or 2 hours)

📞 **BOOKING PROCESS:**
**Day 1:** We call to confirm and clarify missing details, then send customized quote
**Day 2:** Follow-up call and email if no response

⏰ **IMPORTANT TIMING:**
- Bookings are first-come, first-served
- Confirm within 3 working days to secure slots
- We recommend booking 2-3 weeks in advance

💳 **DEPOSIT REQUIREMENTS:**
- 50% deposit needed to secure booking
- Payment via bank transfer or PayNow UEN
- Balance paid onsite on event day
- Government clients: Alternative terms available`
            },
            {
                id: 10,
                category: 'policies',
                question: 'What are the payment methods and terms & conditions?',
                answer: `💳 **ACCEPTED PAYMENT METHODS:**
- Bank transfer
- Credit card (Visa, Master, Amex)
- Cash and cheque
- PayNow UEN
- VENDORS.GOV.SG
- GEBIZ (for government bookings)

📋 **TERMS & CONDITIONS:**
- All players must wear covered footwear (no sandals, heels, crocs, slippers)
- Booking not confirmed until deposit is paid
- Participant numbers can change up to 1 week before event
- Numbers locked 1 week before event with no changes allowed
- No-shows still chargeable
- Additional participants on event day will be charged accordingly
- All participants must sign waiver forms before activity

🏢 **FOR DEPOSIT INVOICE, PROVIDE:**
- Attention to (contact person)
- Mobile number
- Organization name
- Organization billing address

📞 **CONTACT:** (65) 6022-0299`
            },
            {
                id: 11,
                category: 'general',
                question: 'What amenities and facilities are available at Triggered Games?',
                answer: `🏢 **AMENITIES AVAILABLE:**
- Fully air-conditioned venue
- Lockers available for personal belongings
- Toilets available within the mall
- Ample parking lots

📍 **LOCATION BENEFITS:**
- Located in Cathay Cineleisure Orchard
- Easy access to food courts and restaurants
- Shopping and entertainment nearby
- Convenient public transport access

🎯 **EVENT EXPERIENCE:**
- Work in teams to navigate challenges
- Engages all team members simultaneously
- High-energy, immersive experience
- Strategic thinking, problem-solving, agility, and teamwork
- Skills enhance collaboration and quick decision-making
- Guided experience by fun and energetic facilitators
- Winner announcements and group photos
- Team that completes most stages wins!`
            },
            {
                id: 12,
                category: 'general',
                question: 'What is the maximum capacity and how many people can you accommodate?',
                answer: `How many pax can you host?

We have a total of 5 game rooms at our venue and can accommodate up to 46 participants at one time for Package A (1 x 20-minute adventure game) or up to 40 participants for Package B (2 x 20-minute adventure games).

In addition to the adventure games, we also offer an engaging activity called Mystery Hunt. When combined with the adventure games, we can host up to 92 participants for Package A + Mystery Hunt or up to 80 participants for Package B + Mystery Hunt.`
            },
            {
                id: 13,
                category: 'general',
                question: 'Are your facilities wheelchair accessible or suitable for pregnant guests?',
                answer: `For the wheelchair

Our facilities are not currently equipped to accommodate wheelchair users. Some rooms involve running and jumping, and even those that do not, they remain unsuitable for individuals on wheelchair. Additionally, some room require the participants to reach high places, which can present further challenges.

As such, our games are not wheelchair friendly.

Pregnant

Your safety is our top priority, and we want to ensure the best possible experience for all our guests — especially those who are expecting.

Due to the high level of physical activity involved — including jumping, climbing, balancing, and quick movements — this experience is unfortunately not suitable for pregnant guests at any stage of pregnancy.

We truly appreciate your understanding, and if you're still looking for a fun and safe activity, we'd be happy to recommend alternatives that are more pregnancy-friendly.

We would like to recommend our escape room activity at www.trapped.sg instead which is about 6 mins walk away from our Triggered games outlet.

Please find below more details on it:

[75 mins Games]
•	The Attic – Kidnapped themed. Thriller & mystery. Great for those who enjoys a bit of suspense.
•	The Asylum – Investigative themed. Mystery and suspense.
•	The Alchemist's Cabinet of Curiosities – Magic themed. There are no scare factors, and no scary elements are involved, but overall great for those who dislike horror as well.
•	The Great Escape – Prison Themed. Witty and fun. There is a live actor involved albeit a short while.
•	Horror Hostel Warlock – Supernatural themed. Investigative and mystery.
•	Horror The Cursed Video Tape – Thriller themed. Suspense and gritty, there is a live actor involved.
•	The Strangest Things – Science fiction themed, great for those who enjoys mystery.

Do let me know if you prefer our escape room activity and we will proceed to send more details over. 😊`
            },
            {
                id: 14,
                category: 'booking',
                question: 'Do you provide catering or have space for presentations?',
                answer: `Catering

Unfortunately, we do not do catering. We do not have a holding area here and do not allows food and drinks to be consumed within the premises.
While we do not have a dedicated space for presentation and catering, we have some partnership at The Centrepoint which is about 6 minutes walk away.

One of them is We Art that can host the space for you. Their rates is $300/hr for space rental, provided no strong food smells or soup. Please note that the price does not include setting up and cleaning up after catering.

If you would like, we can schedule a site visit so you can view the room as well as the space at We Art.

We Art
Contact Person: Jung Han
Email: <EMAIL>

Else, please find Monster Curry and Dim Sum Place's contacts below that you can liaise with to understand their packages better and book with them for your lunch portion:

Dim Sum Place, #B1-07
Mr Ting – 9812 6119
<EMAIL>
If all pax order the same dish, estimated price would be $25-30. Else, preorder of different meals would vary in price.
Can sit up to 60-70 pax

Monster Planet (by Monster Curry), #01-33C/D
Khalid – 9697 1123
<EMAIL>
Need to book with a min of 12 pax for this menu: https://monsterplanet.com.sg/mega-lunch-special/ - you can also consider the offer below in the picture for your lunch set
Can sit up to 60 pax`
            },
            {
                id: 15,
                category: 'games',
                question: 'What is Mystery Hunt and how does it work with your adventure games?',
                answer: `Mystery Hunt

As the maximum number of pax we can hold over here is 40-46 pax, I would like to introduce another activity we have over here call Mystery Hunt.

This way, half of the group can start with the adventure games while the other half can start with mystery hunt.
Once they are done with their activity, they can then swap over.
To share a bit more about what our Mystery Hunt entails, you can check out the attached PDFs for some sneak peek 😊
The Mystery Hunt is a rain or shine event. This activity would require participants to travel around The Cathay Cineleisure mall. There will be multiple challenges involved. Each group would need to appoint a team leader and a time keeper.

The team leader can divide the groups accordingly to complete the different missions. The groups need not be together to complete the missions, as long as they complete all of them; and correctly, within the amount of time.

Some missions you can expect the participants to do would be to figure out a location based on the latitude and altitude written, finding landmarks based on photos the game master has shown. Once they are at those locations, they will need to do other mini challenges there. Some of the mini challenges include doing a Tiktok inspired video, or taking photos in different poses 😊

This way, they will have "Floor is Lava" and "Mystery Hunt" as a common game for all groups.
These can then be used as a competition mode between the groups.`
            },
            {
                id: 16,
                category: 'booking',
                question: 'What is your follow-up process and how persistent are your sales efforts?',
                answer: `📞 **OUR FOLLOW-UP PROCESS:**

**DAY 1:** Initial response with customized quote and rundown
**DAY 2:** Follow-up call and short email check-in
**DAY 3:** Detailed follow-up highlighting urgency and benefits
**DAY 4:** Site recce invitation + sister outlet introduction (Trapped Escape Room)
**DAY 5:** Final urgency check for immediate bookings
**DAY 6:** Alternative venue options for large groups (50+ pax)

**FINAL SEQUENCE:** Polite closure if no response

🎁 **SPECIAL INCENTIVES:**
- Free gifts for winning teams (min 20 pax)
- Mini-bears or Excel shortcuts mouse pads
- Must book within 3 days to qualify

🏢 **SITE RECCE BENEFITS:**
- Personal venue tour
- See immersive game zones in action
- Discuss event flow and logistics
- Meet our energetic facilitators
- No obligation to book

**OUR APPROACH:**
We follow up professionally but understand if priorities change. Our goal is to ensure you have all information needed to make the best decision for your team.`
            },
            {
                id: 17,
                category: 'policies',
                question: 'What is your refund policy and are there any exceptions?',
                answer: `Refund

Dear Dora,

Thanks for your email.

Unfortunately there is no refund as per the terms governing the sale of the tickets.

As noted on our website, at checkout, and on the booking confirmation email, we're unable to offer refunds for cancellations, missed bookings, no-shows, or late arrivals, regardless of the reason according to our policy.

We are truly sorry about this.

You may find the terms at:

1)	On the website under FAQ
2)	On the email that you forwarded to you us below
3)	On the payment page, before making payment, in which you need to agree to the terms first.`
            },
            {
                id: 18,
                category: 'general',
                question: 'How do you handle feedback and reviews after events?',
                answer: `Feedback

Hi Jia Ying,

Thank you so much for your kind words!

We're thrilled to hear that your team had a fantastic time at Triggered Games. It's always our goal to create memorable experiences for corporate groups like yours.

If you don't mind, we'd greatly appreciate it if you could share your feedback on Google.

A 5-star review from you would mean the world to us and help others discover our awesome games here.

Here's the link to make it easy: https://g.page/r/CTcUfeSZwg7LECA/review

We look forward to hosting your team again soon! Let us know if there's anything else we can do for you.

Goodbye
Hi Joey,

Thanks for letting us know. 😊

Can I know the reason for giving us a miss and can we also find out which activity was selected?

Your feedback is valuable for us to improve our services in the near future.

Thank you for considering Triggered Games for your team bonding event, we look forward to host your group in the near future 😊`
            },
            {
                id: 19,
                category: 'booking',
                question: 'What options do you have for very large groups or alternative venues?',
                answer: `6th Day Email sequence

Dear XXX,

Thanks for your interests in our offsite activities.

Alternatively, we can also run a team building event at an event venue for the following games. Do click on the game links below and you can view the synopsis of the games, rundowns and pricings 😊

Option 1

Mobile Escape Room: Mystery Hunt (Small to Large Scale up to 800 pax)
•	(60 mins/75 mins/90 mins game) Mystery hunt

Complimentary: (30mins game) Ice breaker game - Pipelines

Total duration: 1 hour to 1.5 hours

Option 2

Choose 1 Activity below: (Giant Board Games)
•	(70mins game) Giant Twisted Snakes & Ladders + Toss 'N' Triumph
•	(70mins game) Giant Jenga + Tower of Power
•	(70mins game) Giant Twisted Snakes & Ladders + Tower of Power
•	(70mins game) Giant Jenga + Toss 'N' Triumph

Optional Add-on:( 60 mins/75 mins/90 mins game) Mystery hunt

Complimentary: (30mins game) Ice breaker game - Pipelines

Total duration: 3 hours to 3.5hours

Please find attached the deck for the above games.

At an event venue, you can also have these afterwards after the game:
- Food catering
- Other company agenda/events,
- Speeches by CEO/Management
- Award or ceremonies

If this something that you are keen to find out more, do let us know so we can prepare the timeline of how the game will run and the quote.

Speak soon!`
            }
        ];

        // Initialize the system
        function initializeQA() {
            qaData = [...triggeredQAData, ...moreTriggeredQA];
            renderQA();
            updateStats();
        }

        function renderQA(filteredData = null) {
            const container = document.getElementById('qaContainer');
            const dataToRender = filteredData || qaData;

            container.innerHTML = dataToRender.map(qa => `
                <div class="qa-item" data-category="${qa.category}" data-id="${qa.id}">
                    <div class="qa-header" onclick="toggleQA(${qa.id})">
                        <div class="question">${qa.question}</div>
                        <div class="qa-controls">
                            <span class="collapse-icon" id="icon-${qa.id}">▼</span>
                            <button class="btn btn-sm btn-warning" onclick="event.stopPropagation(); editAnswer(${qa.id})">✏️ Edit</button>
                            <button class="btn btn-sm btn-danger" onclick="event.stopPropagation(); deleteQA(${qa.id})">🗑️ Delete</button>
                        </div>
                    </div>
                    <div class="qa-content" id="content-${qa.id}">
                        <div class="answer-display" id="answer-${qa.id}">${qa.answer}</div>
                        <div class="hidden" id="edit-${qa.id}">
                            <textarea class="answer-edit" id="textarea-${qa.id}">${qa.answer}</textarea>
                            <div class="edit-controls">
                                <button class="btn btn-success btn-sm" onclick="saveAnswer(${qa.id})">💾 Save</button>
                                <button class="btn btn-secondary btn-sm" onclick="cancelEdit(${qa.id})">❌ Cancel</button>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        function toggleQA(id) {
            const content = document.getElementById(`content-${id}`);
            const icon = document.getElementById(`icon-${id}`);

            if (content.classList.contains('active')) {
                content.classList.remove('active');
                icon.textContent = '▼';
                icon.classList.remove('rotated');
            } else {
                content.classList.add('active');
                icon.textContent = '▲';
                icon.classList.add('rotated');
            }
        }

        function editAnswer(id) {
            const answerDiv = document.getElementById(`answer-${id}`);
            const editDiv = document.getElementById(`edit-${id}`);

            answerDiv.classList.add('hidden');
            editDiv.classList.remove('hidden');
        }

        function saveAnswer(id) {
            const textarea = document.getElementById(`textarea-${id}`);
            const newAnswer = textarea.value;

            // Update the data
            const qaItem = qaData.find(qa => qa.id === id);
            if (qaItem) {
                qaItem.answer = newAnswer;
                editedAnswers.add(id);
            }

            // Update the display
            document.getElementById(`answer-${id}`).textContent = newAnswer;
            cancelEdit(id);
            updateStats();
        }

        function cancelEdit(id) {
            const answerDiv = document.getElementById(`answer-${id}`);
            const editDiv = document.getElementById(`edit-${id}`);

            answerDiv.classList.remove('hidden');
            editDiv.classList.add('hidden');
        }

        function deleteQA(id) {
            if (confirm('Are you sure you want to delete this Q&A?')) {
                qaData = qaData.filter(qa => qa.id !== id);
                editedAnswers.delete(id);
                renderQA();
                updateStats();
            }
        }

        function generateNewQA() {
            const newQuestions = [
                {
                    category: 'general',
                    question: 'What should participants wear for Triggered Games?',
                    answer: `👕 **DRESS CODE FOR TRIGGERED GAMES:**
- Comfortable clothing suitable for physical activity
- Covered footwear MANDATORY (no sandals, heels, crocs or slippers)
- Athletic or casual wear recommended
- Avoid loose jewelry or accessories

📝 **PRE-EVENT REQUIREMENTS:**
- Arrive 10-15 minutes early
- All participants must sign waiver forms
- Organize team groupings before arrival
- Be prepared for high-energy physical activity!

⚠️ **SAFETY REMINDERS:**
- Games involve running, jumping, and reaching
- Not suitable for pregnant guests
- Requires good mobility and physical fitness`
                },
                {
                    category: 'booking',
                    question: 'What information do you need for Triggered Games booking?',
                    answer: `**INFORMATION WE NEED FOR BOOKING:**
- Contact person name & mobile number
- Organization name & billing address
- 1st and 2nd contact persons with mobile numbers
- Number of participants (maximum 46 total)
- Preferred date and time
- Package preference (1-5 games per person)
- Any physical limitations or accessibility needs
- Team grouping preferences (organize in advance)`
                },
                {
                    category: 'games',
                    question: 'How do you organize teams for large groups?',
                    answer: `👥 **TEAM ORGANIZATION FOR LARGE GROUPS:**

**ADVANCE PREPARATION:**
- Organize team groupings BEFORE arrival
- Not recommended to group participants onsite
- Reduces game time if done at venue

**CAPACITY MANAGEMENT:**
- Maximum 46 participants total
- Individual room capacities vary (8-10 people)
- Floor is Lava: 10 people per room (Rooms 1 & 2)
- Press It!: 8 people (Room 3)
- Hoops Madness & Hexa Blast: 10 people (Room 4)
- Hide & Seek: 8 people (Room 5)

**FACILITATOR SUPPORT:**
- Facilitators gather pre-assigned groups upon arrival
- No holding areas available (all spaces are game rooms)
- Brief winner announcements and group photos after event
- Professional guidance throughout all games

**LARGE GROUP RECOMMENDATION:**
For 40+ participants, consider combining with Mystery Hunt add-on for maximum engagement!`
                }
            ];

            const randomQuestion = newQuestions[Math.floor(Math.random() * newQuestions.length)];
            const newId = Math.max(...qaData.map(qa => qa.id)) + 1;

            qaData.push({
                id: newId,
                ...randomQuestion
            });

            renderQA();
            updateStats();
            alert('New Triggered Games Q&A generated! Check the bottom of the list.');
        }

        function addCustomQA() {
            const question = prompt('Enter your Triggered Games question:');
            if (!question) return;

            const answer = prompt('Enter the answer:');
            if (!answer) return;

            const category = prompt('Enter category (general/pricing/booking/games/policies):') || 'general';

            const newId = Math.max(...qaData.map(qa => qa.id)) + 1;

            qaData.push({
                id: newId,
                category: category,
                question: question,
                answer: answer
            });

            renderQA();
            updateStats();
        }

        function searchQA(searchTerm) {
            if (!searchTerm.trim()) {
                renderQA();
                return;
            }

            const filtered = qaData.filter(qa =>
                qa.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
                qa.answer.toLowerCase().includes(searchTerm.toLowerCase())
            );

            renderQA(filtered);
        }

        function filterByCategory(category) {
            if (!category) {
                renderQA();
                return;
            }

            const filtered = qaData.filter(qa => qa.category === category);
            renderQA(filtered);
        }

        function updateStats() {
            document.getElementById('totalQA').textContent = qaData.length;
            document.getElementById('editedQA').textContent = editedAnswers.size;

            const categories = [...new Set(qaData.map(qa => qa.category))];
            document.getElementById('categoriesCount').textContent = categories.length;
        }

        function exportQA() {
            const exportSection = document.getElementById('exportSection');
            const exportData = document.getElementById('exportData');

            const exportObject = {
                outlet: 'Triggered Games',
                timestamp: new Date().toISOString(),
                totalQA: qaData.length,
                editedCount: editedAnswers.size,
                data: qaData
            };

            exportData.value = JSON.stringify(exportObject, null, 2);
            exportSection.classList.remove('hidden');
        }

        function copyToClipboard() {
            const exportData = document.getElementById('exportData');
            exportData.select();
            document.execCommand('copy');
            alert('Triggered Games Q&A data copied to clipboard!');
        }

        function downloadJSON() {
            const exportData = document.getElementById('exportData').value;
            const blob = new Blob([exportData], { type: 'application/json' });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = `triggered-qa-data-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // Client Processor Functions
        function toggleClientProcessor() {
            const section = document.getElementById('clientProcessorSection');
            section.classList.toggle('hidden');
        }

        function processClientQuestions() {
            const clientQuestions = document.getElementById('clientQuestions').value.trim();
            if (!clientQuestions) {
                alert('Please enter some client questions first.');
                return;
            }

            const questions = clientQuestions.split('\n').filter(q => q.trim());
            const results = [];

            questions.forEach((question, index) => {
                const matches = findMatchingAnswers(question);
                results.push({
                    question: question.trim(),
                    matches: matches
                });
            });

            displayProcessedResults(results);
        }

        function findMatchingAnswers(question) {
            const keywords = extractKeywords(question.toLowerCase());
            const matches = [];

            qaData.forEach(qa => {
                let score = 0;
                const qaText = (qa.question + ' ' + qa.answer).toLowerCase();

                keywords.forEach(keyword => {
                    if (qaText.includes(keyword)) {
                        score++;
                    }
                });

                if (score > 0) {
                    matches.push({
                        qa: qa,
                        score: score,
                        relevance: (score / keywords.length) * 100
                    });
                }
            });

            // Sort by relevance score (highest first)
            return matches.sort((a, b) => b.score - a.score).slice(0, 3); // Top 3 matches
        }

        function extractKeywords(text) {
            // Enhanced keyword extraction with business-specific terms
            const commonWords = ['the', 'is', 'at', 'which', 'on', 'a', 'an', 'and', 'or', 'but', 'in', 'with', 'to', 'for', 'of', 'as', 'by', 'from', 'up', 'about', 'into', 'through', 'during', 'before', 'after', 'above', 'below', 'between', 'among', 'can', 'could', 'should', 'would', 'will', 'do', 'does', 'did', 'have', 'has', 'had', 'be', 'been', 'being', 'am', 'are', 'was', 'were', 'what', 'when', 'where', 'why', 'how', 'who', 'whom', 'whose', 'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them', 'my', 'your', 'his', 'her', 'its', 'our', 'their'];

            // Business-specific keyword mappings
            const keywordMappings = {
                'cost': ['price', 'pricing', 'rate', 'fee', 'charge', 'payment'],
                'price': ['cost', 'pricing', 'rate', 'fee', 'charge', 'payment'],
                'pricing': ['cost', 'price', 'rate', 'fee', 'charge', 'payment'],
                'book': ['booking', 'reserve', 'reservation', 'schedule'],
                'booking': ['book', 'reserve', 'reservation', 'schedule'],
                'cancel': ['cancellation', 'refund', 'reschedule'],
                'cancellation': ['cancel', 'refund', 'reschedule'],
                'game': ['games', 'activity', 'activities', 'challenge'],
                'games': ['game', 'activity', 'activities', 'challenge'],
                'team': ['group', 'corporate', 'company'],
                'group': ['team', 'corporate', 'company'],
                'people': ['pax', 'participants', 'persons'],
                'pax': ['people', 'participants', 'persons'],
                'participants': ['people', 'pax', 'persons'],
                'time': ['timing', 'schedule', 'duration', 'hours'],
                'timing': ['time', 'schedule', 'duration', 'hours'],
                'location': ['address', 'where', 'venue'],
                'address': ['location', 'where', 'venue'],
                'payment': ['pay', 'deposit', 'invoice', 'billing'],
                'pay': ['payment', 'deposit', 'invoice', 'billing']
            };

            let words = text.replace(/[^\w\s]/g, '').split(/\s+/);
            let keywords = words.filter(word =>
                word.length > 2 &&
                !commonWords.includes(word.toLowerCase()) &&
                !word.match(/^\d+$/) // Remove pure numbers
            );

            // Add related keywords based on mappings
            const expandedKeywords = [...keywords];
            keywords.forEach(keyword => {
                const lowerKeyword = keyword.toLowerCase();
                if (keywordMappings[lowerKeyword]) {
                    expandedKeywords.push(...keywordMappings[lowerKeyword]);
                }
            });

            return [...new Set(expandedKeywords.map(k => k.toLowerCase()))]; // Remove duplicates and normalize
        }

        function displayProcessedResults(results) {
            const resultsContainer = document.getElementById('resultsContainer');
            const processedResults = document.getElementById('processedResults');

            if (results.length === 0) {
                resultsContainer.innerHTML = '<div class="no-match">No questions found to process.</div>';
                processedResults.style.display = 'block';
                return;
            }

            let html = '';
            results.forEach((result, index) => {
                html += `<div class="result-item">
                    <div class="result-question">Q${index + 1}: ${result.question}</div>`;

                if (result.matches.length > 0) {
                    result.matches.forEach((match, matchIndex) => {
                        html += `
                            <div style="margin-bottom: 15px; border-left: 3px solid #28a745; padding-left: 10px;">
                                <div style="font-weight: bold; color: #28a745; margin-bottom: 5px;">
                                    Match ${matchIndex + 1} (${Math.round(match.relevance)}% relevance):
                                </div>
                                <div style="font-weight: bold; margin-bottom: 5px;">${match.qa.question}</div>
                                <div class="result-answer">${match.qa.answer}</div>
                                <button class="copy-answer-btn" onclick="copyAnswer('${match.qa.id}', ${index}, ${matchIndex})">
                                    📋 Copy Answer
                                </button>
                            </div>`;
                    });
                } else {
                    html += '<div class="no-match">No matching answers found for this question.</div>';
                }

                html += '</div>';
            });

            resultsContainer.innerHTML = html;
            processedResults.style.display = 'block';
        }

        function copyAnswer(qaId, questionIndex, matchIndex) {
            const qa = qaData.find(q => q.id == qaId);
            if (qa) {
                navigator.clipboard.writeText(qa.answer).then(() => {
                    // Visual feedback
                    const button = event.target;
                    const originalText = button.textContent;
                    button.textContent = '✅ Copied!';
                    button.style.background = '#1e7e34';

                    setTimeout(() => {
                        button.textContent = originalText;
                        button.style.background = '#28a745';
                    }, 2000);
                }).catch(err => {
                    // Fallback for older browsers
                    const textArea = document.createElement('textarea');
                    textArea.value = qa.answer;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);

                    alert('Answer copied to clipboard!');
                });
            }
        }

        function clearClientProcessor() {
            document.getElementById('clientQuestions').value = '';
            document.getElementById('resultsContainer').innerHTML = '';
            document.getElementById('processedResults').style.display = 'none';
        }

        // Initialize when page loads
        window.onload = function() {
            initializeQA();
        };
    </script>
</body>
</html>
