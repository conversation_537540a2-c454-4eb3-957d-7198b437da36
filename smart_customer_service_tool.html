<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Adventure Entertainment - Smart Customer Service Tool</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .main-content {
            padding: 30px;
        }
        
        .question-input-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        
        .question-textarea {
            width: 100%;
            height: 120px;
            padding: 15px;
            font-size: 16px;
            border: 2px solid #ddd;
            border-radius: 8px;
            margin-bottom: 15px;
            resize: vertical;
        }
        
        .analyze-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
        }
        
        .analyze-btn:hover {
            background: #218838;
        }
        
        .suggestions-section {
            background: #e8f5e8;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 30px;
            display: none;
        }
        
        .suggestion-item {
            background: white;
            padding: 15px;
            margin-bottom: 15px;
            border-radius: 8px;
            border-left: 4px solid #28a745;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .suggestion-item:hover {
            transform: translateX(5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .suggestion-title {
            font-weight: bold;
            color: #28a745;
            margin-bottom: 5px;
        }
        
        .suggestion-preview {
            color: #666;
            font-size: 14px;
        }
        
        .response-area {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            min-height: 200px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        
        .copy-btn {
            background: #17a2b8;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 15px;
            font-size: 16px;
        }
        
        .copy-btn:hover {
            background: #138496;
        }
        
        .tabs {
            display: flex;
            background: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .tab {
            flex: 1;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .tab.active {
            background: #667eea;
            color: white;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            display: none;
        }
        
        .admin-section {
            background: #fff3cd;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        
        .admin-form {
            display: grid;
            gap: 15px;
        }
        
        .admin-input {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .admin-textarea {
            height: 100px;
            resize: vertical;
        }
        
        .add-btn {
            background: #ffc107;
            color: #212529;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
        }
        
        .knowledge-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
        }
        
        .knowledge-item {
            background: #f8f9fa;
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 5px;
            border-left: 3px solid #ffc107;
        }
        
        .calc-section {
            background: #e8f5e8;
            padding: 25px;
            border-radius: 10px;
        }
        
        .calc-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .calc-input {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .calc-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
        }
        
        .result-section {
            background: #fff3cd;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
            border-left: 4px solid #ffc107;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 Smart Customer Service Tool</h1>
            <p>Adventure Entertainment Singapore - AI-Powered Response System</p>
        </div>
        
        <div class="main-content">
            <div class="tabs">
                <div class="tab active" onclick="showTab('smart-search')">🔍 Smart Search</div>
                <div class="tab" onclick="showTab('calculator')">💰 Price Calculator</div>
                <div class="tab" onclick="showTab('knowledge')">📚 Knowledge Base</div>
            </div>
            
            <!-- SMART SEARCH TAB -->
            <div id="smart-search" class="tab-content active">
                <div class="question-input-section">
                    <h3>🔍 Paste Customer Question Here</h3>
                    <textarea class="question-textarea" id="customerQuestion" placeholder="Paste the customer's email or question here...

Example:
'Hi, we are planning a team building event for 25 people. What are your rates and what activities do you have?'"></textarea>
                    
                    <button class="analyze-btn" onclick="analyzeQuestion()">🤖 Find Best Response</button>
                </div>
                
                <div class="suggestions-section" id="suggestionsSection">
                    <h3>💡 Suggested Responses</h3>
                    <div id="suggestionsList"></div>
                </div>
                
                <div class="response-area" id="responseArea">
                    Paste a customer question above and click "Find Best Response" to get instant, accurate answers!
                </div>
                
                <button class="copy-btn" onclick="copyResponse()">📋 Copy Response</button>
                <div class="success-message" id="copySuccess">✅ Response copied to clipboard!</div>
            </div>
            
            <!-- CALCULATOR TAB -->
            <div id="calculator" class="tab-content">
                <div class="calc-section">
                    <h3>💰 Accurate Price Calculator</h3>
                    
                    <div class="calc-grid">
                        <select class="calc-input" id="outlet">
                            <option value="">Select Outlet</option>
                            <option value="triggered">Triggered Games</option>
                            <option value="trapped">Trapped Escape Room</option>
                        </select>
                        
                        <input type="number" class="calc-input" id="participants" placeholder="Number of participants" min="1">
                        
                        <select class="calc-input" id="timing">
                            <option value="">Select Timing</option>
                            <option value="off-peak">Off-peak</option>
                            <option value="peak">Peak</option>
                        </select>
                        
                        <input type="number" class="calc-input" id="games" placeholder="Games per person" min="1" max="5">
                        
                        <label style="display: flex; align-items: center; gap: 10px;">
                            <input type="checkbox" id="mysteryHunt"> Add Mystery Hunt (+$20/person)
                        </label>
                        
                        <label style="display: flex; align-items: center; gap: 10px;">
                            <input type="checkbox" id="earlyBird"> Early Bird (Trapped only, -$3/game)
                        </label>
                    </div>
                    
                    <button class="calc-btn" onclick="calculateAccuratePrice()">Calculate Accurate Quote</button>
                    
                    <div class="result-section" id="calcResult" style="display: none;">
                        <h4>💰 Your Accurate Quote:</h4>
                        <div id="quoteDetails"></div>
                        <button class="copy-btn" onclick="copyQuote()">📋 Copy Quote</button>
                    </div>
                </div>
            </div>
            
            <!-- KNOWLEDGE BASE TAB -->
            <div id="knowledge" class="tab-content">
                <div class="admin-section">
                    <h3>📚 Add New Question & Answer</h3>
                    <p>Build your knowledge base by adding real customer questions and your best responses:</p>
                    
                    <div class="admin-form">
                        <input type="text" class="admin-input" id="newKeywords" placeholder="Keywords (e.g., pricing, group discount, booking)">
                        <textarea class="admin-input admin-textarea" id="newQuestion" placeholder="Customer question example..."></textarea>
                        <textarea class="admin-input admin-textarea" id="newAnswer" placeholder="Your response template..."></textarea>
                        <button class="add-btn" onclick="addKnowledge()">➕ Add to Knowledge Base</button>
                    </div>
                </div>
                
                <div class="admin-section">
                    <h3>📋 Current Knowledge Base</h3>
                    <div class="knowledge-list" id="knowledgeList"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Knowledge base with accurate information
        let knowledgeBase = [
            {
                keywords: ['price', 'pricing', 'cost', 'rate', 'how much'],
                question: 'What are your prices?',
                answer: `Hi [Customer Name],

Thank you for your interest! Here's our accurate pricing:

🏠 **TRAPPED ESCAPE ROOM (75 mins each)**
Off-peak (Mon-Fri before 6pm):
• 1-19 pax: $28.90 per game
• 20-39 pax: $27.90 per game  
• 40+ pax: $26.90 per game

Peak (Weekdays after 6pm, weekends & holidays):
• 1-19 pax: $34.90 per game
• 20-39 pax: $33.90 per game
• 40+ pax: $32.90 per game

🎮 **TRIGGERED GAMES (20 mins each)**
Off-peak (Mon-Thu all day, Fri before 6pm):
• 1-19 pax: $16.00 per game
• 20-39 pax: $14.40 per game
• 40+ pax: $13.60 per game

Peak (Fri after 6pm, weekends & holidays):
• 1-19 pax: $20.00 per game
• 20-39 pax: $18.00 per game
• 40+ pax: $17.00 per game

*Trapped prices exclude GST, Triggered prices include GST*

To provide an accurate quote, please let me know:
1. Number of participants
2. Preferred date and time
3. Which outlet interests you
4. How many games per person

Best regards,
[Your Name]
Adventure Entertainment Singapore`
            },
            {
                keywords: ['group', 'discount', 'bulk', 'corporate', 'team building'],
                question: 'Do you have group discounts?',
                answer: `Hi [Customer Name],

Yes! We offer excellent group discounts:

🏠 **TRAPPED ESCAPE ROOM:**
• 20-39 pax: $1 off per game per person
• 40+ pax: $2 off per game per person

🎮 **TRIGGERED GAMES:**
• 20-39 pax: $1.60 off per game per person
• 40+ pax: $2.40-$3.40 off per game per person

💰 **ADDITIONAL SAVINGS:**
• Early bird (Trapped only): $3 off per game if deposit paid 3 months ahead
• Mystery Hunt: $20/person (great for large groups)

**EXAMPLE for 25 people (Triggered, off-peak, 2 games):**
Regular: 25 × $16 × 2 = $800
Group rate: 25 × $14.40 × 2 = $720
**You save $80!**

Would you like me to calculate exact savings for your group?

Best regards,
[Your Name]
Adventure Entertainment Singapore`
            }
        ];
        
        // Load saved knowledge base
        function loadKnowledgeBase() {
            const saved = localStorage.getItem('knowledgeBase');
            if (saved) {
                knowledgeBase = JSON.parse(saved);
            }
            displayKnowledgeBase();
        }
        
        // Save knowledge base
        function saveKnowledgeBase() {
            localStorage.setItem('knowledgeBase', JSON.stringify(knowledgeBase));
        }
        
        // Tab switching
        function showTab(tabName) {
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
        }
        
        // Smart question analysis
        function analyzeQuestion() {
            const question = document.getElementById('customerQuestion').value.toLowerCase();
            
            if (!question.trim()) {
                alert('Please paste a customer question first!');
                return;
            }
            
            const suggestions = [];
            
            // Find matching responses
            knowledgeBase.forEach((item, index) => {
                let score = 0;
                item.keywords.forEach(keyword => {
                    if (question.includes(keyword)) {
                        score++;
                    }
                });
                
                if (score > 0) {
                    suggestions.push({
                        ...item,
                        score: score,
                        index: index
                    });
                }
            });
            
            // Sort by relevance
            suggestions.sort((a, b) => b.score - a.score);
            
            displaySuggestions(suggestions);
        }
        
        // Display suggestions
        function displaySuggestions(suggestions) {
            const suggestionsSection = document.getElementById('suggestionsSection');
            const suggestionsList = document.getElementById('suggestionsList');
            
            if (suggestions.length === 0) {
                suggestionsList.innerHTML = '<p>No matching responses found. Try adding this question to the knowledge base!</p>';
            } else {
                suggestionsList.innerHTML = suggestions.map(suggestion => `
                    <div class="suggestion-item" onclick="selectSuggestion(${suggestion.index})">
                        <div class="suggestion-title">📝 ${suggestion.question}</div>
                        <div class="suggestion-preview">${suggestion.answer.substring(0, 100)}...</div>
                    </div>
                `).join('');
            }
            
            suggestionsSection.style.display = 'block';
        }
        
        // Select suggestion
        function selectSuggestion(index) {
            const response = knowledgeBase[index].answer;
            document.getElementById('responseArea').textContent = response;
        }
        
        // Copy response
        function copyResponse() {
            const responseText = document.getElementById('responseArea').textContent;
            navigator.clipboard.writeText(responseText).then(() => {
                document.getElementById('copySuccess').style.display = 'block';
                setTimeout(() => {
                    document.getElementById('copySuccess').style.display = 'none';
                }, 3000);
            });
        }
        
        // Add new knowledge
        function addKnowledge() {
            const keywords = document.getElementById('newKeywords').value.split(',').map(k => k.trim().toLowerCase());
            const question = document.getElementById('newQuestion').value;
            const answer = document.getElementById('newAnswer').value;
            
            if (!keywords[0] || !question || !answer) {
                alert('Please fill in all fields!');
                return;
            }
            
            knowledgeBase.push({
                keywords: keywords,
                question: question,
                answer: answer
            });
            
            saveKnowledgeBase();
            displayKnowledgeBase();
            
            // Clear form
            document.getElementById('newKeywords').value = '';
            document.getElementById('newQuestion').value = '';
            document.getElementById('newAnswer').value = '';
            
            alert('Knowledge added successfully!');
        }
        
        // Display knowledge base
        function displayKnowledgeBase() {
            const knowledgeList = document.getElementById('knowledgeList');
            knowledgeList.innerHTML = knowledgeBase.map((item, index) => `
                <div class="knowledge-item">
                    <strong>Keywords:</strong> ${item.keywords.join(', ')}<br>
                    <strong>Question:</strong> ${item.question}<br>
                    <strong>Answer:</strong> ${item.answer.substring(0, 100)}...
                </div>
            `).join('');
        }
        
        // Accurate price calculator
        function calculateAccuratePrice() {
            const outlet = document.getElementById('outlet').value;
            const participants = parseInt(document.getElementById('participants').value);
            const timing = document.getElementById('timing').value;
            const games = parseInt(document.getElementById('games').value);
            const mysteryHunt = document.getElementById('mysteryHunt').checked;
            const earlyBird = document.getElementById('earlyBird').checked;

            if (!outlet || !participants || !timing || !games) {
                alert('Please fill in all fields');
                return;
            }

            let basePrice = 0;
            let gstNote = '';

            // Accurate pricing based on product info
            if (outlet === 'triggered') {
                if (timing === 'off-peak') {
                    if (participants >= 40) basePrice = 13.60;
                    else if (participants >= 20) basePrice = 14.40;
                    else basePrice = 16.00;
                } else {
                    if (participants >= 40) basePrice = 17.00;
                    else if (participants >= 20) basePrice = 18.00;
                    else basePrice = 20.00;
                }
                gstNote = '(GST included)';
            } else {
                if (timing === 'off-peak') {
                    if (participants >= 40) basePrice = 26.90;
                    else if (participants >= 20) basePrice = 27.90;
                    else basePrice = 28.90;
                } else {
                    if (participants >= 40) basePrice = 32.90;
                    else if (participants >= 20) basePrice = 33.90;
                    else basePrice = 34.90;
                }
                gstNote = '(GST not included)';
            }

            let totalBase = participants * basePrice * games;
            let earlyBirdDiscount = 0;
            let additionalCosts = 0;
            let additionalText = '';

            // Apply early bird discount (Trapped only, requires 15+ people)
            if (earlyBird && outlet === 'trapped' && participants >= 15) {
                earlyBirdDiscount = participants * games * 3;
            }

            // Add Mystery Hunt
            if (mysteryHunt) {
                additionalCosts = participants * 20;
                additionalText = `Mystery Hunt: ${participants} × $20 = $${additionalCosts}`;
            }

            let finalPrice = totalBase - earlyBirdDiscount + additionalCosts;

            // Calculate group discount savings
            let standardPrice = 0;
            if (outlet === 'triggered') {
                standardPrice = participants * (timing === 'off-peak' ? 16 : 20) * games;
            } else {
                standardPrice = participants * (timing === 'off-peak' ? 28.90 : 34.90) * games;
            }

            let groupSavings = standardPrice - totalBase;
            let totalSavings = groupSavings + earlyBirdDiscount;

            const quoteHtml = `
                <strong>${outlet.toUpperCase()} - ${timing.toUpperCase()}</strong><br>
                ${participants} participants × ${games} games × $${basePrice} = $${totalBase.toFixed(2)}<br>
                ${earlyBirdDiscount > 0 ? `Early bird discount: -$${earlyBirdDiscount.toFixed(2)}<br>` : ''}
                ${additionalText ? `${additionalText}<br>` : ''}
                <strong>TOTAL: $${finalPrice.toFixed(2)} ${gstNote}</strong><br>
                <em>Deposit required: $${(finalPrice * 0.5).toFixed(2)} (50%)</em><br><br>
                ${totalSavings > 0 ? `<em>💰 You save: $${totalSavings.toFixed(2)} with group/early bird discounts!</em>` : ''}
            `;

            document.getElementById('quoteDetails').innerHTML = quoteHtml;
            document.getElementById('calcResult').style.display = 'block';
        }

        // Copy quote
        function copyQuote() {
            const quoteText = document.getElementById('quoteDetails').textContent;
            navigator.clipboard.writeText(quoteText);
        }

        // Initialize
        window.onload = function() {
            loadKnowledgeBase();
        };
    </script>
</body>
</html>
