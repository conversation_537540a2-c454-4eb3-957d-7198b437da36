<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Adventure Entertainment - AI Learning Service Tool</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .main-content {
            padding: 30px;
        }
        
        .tabs {
            display: flex;
            background: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .tab {
            flex: 1;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .tab.active {
            background: #667eea;
            color: white;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .outlet-selector {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .outlet-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        
        .outlet-btn {
            padding: 20px;
            border: 3px solid #ddd;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }
        
        .outlet-btn.active {
            border-color: #667eea;
            background: #f0f4ff;
        }
        
        .outlet-title {
            font-size: 1.5em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .question-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        
        .question-textarea {
            width: 100%;
            height: 120px;
            padding: 15px;
            font-size: 16px;
            border: 2px solid #ddd;
            border-radius: 8px;
            margin-bottom: 15px;
            resize: vertical;
        }
        
        .analyze-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .extract-btn {
            background: #17a2b8;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
        }
        
        .keywords-section {
            background: #e8f5e8;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            display: none;
        }
        
        .keyword-tag {
            display: inline-block;
            background: #28a745;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            margin: 5px;
            font-size: 12px;
        }
        
        .suggestions-section {
            background: #e8f5e8;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 30px;
            display: none;
        }
        
        .suggestion-item {
            background: white;
            padding: 15px;
            margin-bottom: 15px;
            border-radius: 8px;
            border-left: 4px solid #28a745;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .suggestion-item:hover {
            transform: translateX(5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .suggestion-title {
            font-weight: bold;
            color: #28a745;
            margin-bottom: 5px;
        }
        
        .confidence-score {
            background: #ffc107;
            color: #212529;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 11px;
            font-weight: bold;
            float: right;
        }
        
        .response-area {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            min-height: 200px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        
        .learning-section {
            background: #fff3cd;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        
        .learning-form {
            display: grid;
            gap: 15px;
        }
        
        .form-input {
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .form-textarea {
            height: 100px;
            resize: vertical;
        }
        
        .add-btn {
            background: #ffc107;
            color: #212529;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            font-size: 16px;
        }
        
        .knowledge-list {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
        }
        
        .knowledge-item {
            background: #f8f9fa;
            padding: 15px;
            margin-bottom: 15px;
            border-radius: 5px;
            border-left: 3px solid #ffc107;
        }
        
        .knowledge-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .delete-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .copy-btn {
            background: #17a2b8;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 15px;
            font-size: 16px;
        }
        
        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            display: none;
        }
        
        .stats-section {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .stat-item {
            text-align: center;
            padding: 15px;
            background: white;
            border-radius: 5px;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #1976d2;
        }
        
        .no-match-section {
            background: #fff3cd;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
            border-left: 4px solid #ffc107;
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧠 AI Learning Service Tool</h1>
            <p>Adventure Entertainment Singapore - Smart Learning Customer Service System</p>
        </div>
        
        <div class="main-content">
            <div class="tabs">
                <div class="tab active" onclick="showTab('smart-search')">🔍 Smart Search</div>
                <div class="tab" onclick="showTab('learning')">📚 Add Knowledge</div>
                <div class="tab" onclick="showTab('manage')">⚙️ Manage Knowledge</div>
                <div class="tab" onclick="showTab('calculator')">💰 Calculator</div>
            </div>
            
            <!-- SMART SEARCH TAB -->
            <div id="smart-search" class="tab-content active">
                <div class="outlet-selector">
                    <h3>🏢 Select Outlet:</h3>
                    <div class="outlet-buttons">
                        <div class="outlet-btn" onclick="selectOutlet('trapped')">
                            <div class="outlet-title">🏠 TRAPPED ESCAPE ROOM</div>
                            <div style="color: #666; font-size: 14px;">75-min escape experiences</div>
                        </div>
                        <div class="outlet-btn" onclick="selectOutlet('triggered')">
                            <div class="outlet-title">🎮 TRIGGERED GAMES</div>
                            <div style="color: #666; font-size: 14px;">20-min interactive games</div>
                        </div>
                    </div>
                </div>
                
                <div class="question-section">
                    <h3>📝 Paste Customer Question Here</h3>
                    <textarea class="question-textarea" id="customerQuestion" placeholder="Paste the customer's email or question here...

Example:
'Hi, we are planning a team building event for 25 people. What are your rates and what activities do you have?'"></textarea>
                    
                    <button class="analyze-btn" onclick="analyzeQuestion()">🔍 Find Best Response</button>
                    <button class="extract-btn" onclick="extractKeywords()">🏷️ Extract Keywords</button>
                </div>
                
                <div class="keywords-section" id="keywordsSection">
                    <h4>🏷️ Extracted Keywords:</h4>
                    <div id="keywordsList"></div>
                </div>
                
                <div class="suggestions-section" id="suggestionsSection">
                    <h3>💡 Suggested Responses (Ranked by Relevance)</h3>
                    <div id="suggestionsList"></div>
                </div>
                
                <div class="no-match-section" id="noMatchSection">
                    <h4>❓ No Perfect Match Found</h4>
                    <p>This seems like a new type of question! Consider adding it to the knowledge base using the "Add Knowledge" tab.</p>
                    <p><strong>Extracted keywords:</strong> <span id="noMatchKeywords"></span></p>
                </div>
                
                <div class="response-area" id="responseArea">
                    Select an outlet and paste a customer question to get started!
                </div>
                
                <button class="copy-btn" onclick="copyResponse()">📋 Copy Response</button>
                <div class="success-message" id="copySuccess">✅ Response copied to clipboard!</div>
            </div>
            
            <!-- LEARNING TAB -->
            <div id="learning" class="tab-content">
                <div class="stats-section">
                    <h3>📊 Knowledge Base Statistics</h3>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-number" id="trappedCount">0</div>
                            <div>Trapped Q&As</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="triggeredCount">0</div>
                            <div>Triggered Q&As</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="totalKeywords">0</div>
                            <div>Total Keywords</div>
                        </div>
                    </div>
                </div>
                
                <div class="learning-section">
                    <h3>📚 Add New Question & Answer</h3>
                    <p>Train the system with real customer questions and your best responses:</p>
                    
                    <div class="learning-form">
                        <select class="form-input" id="newOutlet">
                            <option value="">Select Outlet</option>
                            <option value="trapped">Trapped Escape Room</option>
                            <option value="triggered">Triggered Games</option>
                        </select>
                        
                        <textarea class="form-input form-textarea" id="newCustomerQuestion" placeholder="Paste the actual customer question here...

Example: 'Hi, we're looking for team building activities for our company. We have about 30 people. What do you recommend and what are the costs?'"></textarea>
                        
                        <button class="extract-btn" onclick="extractKeywordsFromNew()">🏷️ Auto-Extract Keywords</button>
                        
                        <input type="text" class="form-input" id="newKeywords" placeholder="Keywords (auto-extracted or manually add more, separated by commas)">
                        
                        <textarea class="form-input form-textarea" id="newAnswer" placeholder="Your best response template...

Use [Customer Name] for personalization."></textarea>
                        
                        <button class="add-btn" onclick="addKnowledge()">➕ Add to Knowledge Base</button>
                    </div>
                </div>
            </div>
            
            <!-- MANAGE KNOWLEDGE TAB -->
            <div id="manage" class="tab-content">
                <div class="learning-section">
                    <h3>⚙️ Manage Knowledge Base</h3>
                    <p>View, edit, and organize your learned Q&A pairs:</p>
                    
                    <div style="margin-bottom: 20px;">
                        <select class="form-input" id="filterOutlet" onchange="filterKnowledge()" style="width: 200px;">
                            <option value="">All Outlets</option>
                            <option value="trapped">Trapped Only</option>
                            <option value="triggered">Triggered Only</option>
                        </select>
                        
                        <button class="add-btn" onclick="exportKnowledge()" style="margin-left: 10px;">📤 Export</button>
                        <input type="file" id="importFile" accept=".json" style="display: none;" onchange="importKnowledge()">
                        <button class="add-btn" onclick="document.getElementById('importFile').click()" style="margin-left: 10px;">📥 Import</button>
                    </div>
                    
                    <div class="knowledge-list" id="knowledgeList"></div>
                </div>
            </div>
            
            <!-- CALCULATOR TAB -->
            <div id="calculator" class="tab-content">
                <div class="learning-section">
                    <h3>💰 Price Calculator</h3>
                    <p>Quick pricing for customer inquiries:</p>
                    
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px;">
                        <select class="form-input" id="calcOutlet">
                            <option value="">Select Outlet</option>
                            <option value="triggered">Triggered Games</option>
                            <option value="trapped">Trapped Escape Room</option>
                        </select>
                        
                        <input type="number" class="form-input" id="calcParticipants" placeholder="Number of participants" min="1">
                        
                        <select class="form-input" id="calcTiming">
                            <option value="">Select Timing</option>
                            <option value="off-peak">Off-peak</option>
                            <option value="peak">Peak</option>
                        </select>
                        
                        <input type="number" class="form-input" id="calcGames" placeholder="Games per person" min="1" max="5">
                    </div>
                    
                    <button class="analyze-btn" onclick="calculatePrice()">Calculate Quote</button>
                    
                    <div class="response-area" id="calcResult" style="display: none; margin-top: 20px;">
                        <div id="quoteDetails"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentOutlet = null;
        let knowledgeBase = {
            trapped: [
                {
                    keywords: ['price', 'pricing', 'cost', 'rate', 'how much', 'expensive', 'cheap', 'budget'],
                    customerQuestion: 'What are your escape room prices?',
                    answer: `Hi [Customer Name],

Thank you for your interest in our escape room experiences!

🏠 **TRAPPED ESCAPE ROOM PRICING (75 mins each)**

**OFF-PEAK (Mon-Fri before 6pm):**
• 1-19 people: $28.90 per game per person
• 20-39 people: $27.90 per game per person  
• 40+ people: $26.90 per game per person

**PEAK (Weekdays after 6pm, weekends & holidays):**
• 1-19 people: $34.90 per game per person
• 20-39 people: $33.90 per game per person
• 40+ people: $32.90 per game per person

**EARLY BIRD SPECIAL:**
Additional $3 discount per game per person if deposit paid 3 months in advance (minimum 15 people)

*All prices exclude GST*

To provide an accurate quote, please let me know:
1. Number of participants
2. Preferred date and time
3. How many games per person

Best regards,
[Your Name]
Adventure Entertainment Singapore`
                }
            ],
            triggered: [
                {
                    keywords: ['price', 'pricing', 'cost', 'rate', 'how much', 'expensive', 'cheap', 'budget'],
                    customerQuestion: 'What are your Triggered Games prices?',
                    answer: `Hi [Customer Name],

Thank you for your interest in our high-energy interactive games!

🎮 **TRIGGERED GAMES PRICING (20 mins each)**

**OFF-PEAK (Mon-Thu all day, Fri before 6pm):**
• 1-19 people: $16.00 per game per person
• 20-39 people: $14.40 per game per person
• 40+ people: $13.60 per game per person

**PEAK (Fri after 6pm, weekends & holidays):**
• 1-19 people: $20.00 per game per person
• 20-39 people: $18.00 per game per person
• 40+ people: $17.00 per game per person

*All prices include GST*

**CAPACITY:** Maximum 46 participants across all game rooms

To provide an accurate quote, please let me know:
1. Number of participants
2. Preferred date and time
3. How many games per person

Best regards,
[Your Name]
Adventure Entertainment Singapore`
                }
            ]
        };
        
        // Load saved knowledge base
        function loadKnowledgeBase() {
            const saved = localStorage.getItem('adventureKnowledgeBase');
            if (saved) {
                knowledgeBase = JSON.parse(saved);
            }
            updateStats();
            displayKnowledgeBase();
        }
        
        // Save knowledge base
        function saveKnowledgeBase() {
            localStorage.setItem('adventureKnowledgeBase', JSON.stringify(knowledgeBase));
            updateStats();
        }
        
        // Tab switching
        function showTab(tabName) {
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
        }
        
        // Select outlet
        function selectOutlet(outlet) {
            currentOutlet = outlet;
            
            document.querySelectorAll('.outlet-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            event.target.closest('.outlet-btn').classList.add('active');
            
            document.getElementById('responseArea').textContent = `${outlet.toUpperCase()} selected! Paste a customer question below.`;
        }
        
        // Smart keyword extraction
        function extractKeywords() {
            const question = document.getElementById('customerQuestion').value.toLowerCase();
            
            if (!question.trim()) {
                alert('Please paste a customer question first!');
                return;
            }
            
            const keywords = smartExtractKeywords(question);
            displayKeywords(keywords);
        }
        
        function smartExtractKeywords(text) {
            // Common words to ignore
            const stopWords = ['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'hi', 'hello', 'we', 'our', 'us', 'you', 'your', 'i', 'my', 'me'];
            
            // Business-specific important keywords
            const importantKeywords = ['price', 'pricing', 'cost', 'rate', 'book', 'booking', 'reserve', 'team', 'building', 'corporate', 'group', 'escape', 'room', 'game', 'activity', 'people', 'person', 'pax', 'discount', 'package', 'date', 'time', 'weekend', 'weekday', 'available', 'capacity'];
            
            const words = text.toLowerCase().match(/\b\w+\b/g) || [];
            const keywords = [];
            
            words.forEach(word => {
                if (word.length > 2 && !stopWords.includes(word)) {
                    // Prioritize business keywords
                    if (importantKeywords.includes(word)) {
                        keywords.push(word);
                    }
                    // Add numbers (for group sizes)
                    else if (/^\d+$/.test(word)) {
                        keywords.push(word + ' people');
                    }
                    // Add other meaningful words
                    else if (word.length > 4) {
                        keywords.push(word);
                    }
                }
            });
            
            // Remove duplicates and return
            return [...new Set(keywords)];
        }
        
        function displayKeywords(keywords) {
            const keywordsSection = document.getElementById('keywordsSection');
            const keywordsList = document.getElementById('keywordsList');
            
            keywordsList.innerHTML = keywords.map(keyword => 
                `<span class="keyword-tag">${keyword}</span>`
            ).join('');
            
            keywordsSection.style.display = 'block';
        }
        
        // Analyze question with smart matching
        function analyzeQuestion() {
            if (!currentOutlet) {
                alert('Please select an outlet first!');
                return;
            }

            const question = document.getElementById('customerQuestion').value.toLowerCase();

            if (!question.trim()) {
                alert('Please paste a customer question first!');
                return;
            }

            const questionKeywords = smartExtractKeywords(question);
            displayKeywords(questionKeywords);

            const knowledge = knowledgeBase[currentOutlet] || [];
            const suggestions = [];

            // Advanced matching algorithm
            knowledge.forEach((item, index) => {
                let score = 0;
                let matchedKeywords = [];

                // Check keyword matches
                item.keywords.forEach(keyword => {
                    if (questionKeywords.some(qk => qk.includes(keyword) || keyword.includes(qk))) {
                        score += 2; // Exact or partial match
                        matchedKeywords.push(keyword);
                    }
                });

                // Check question similarity
                const questionWords = question.split(' ');
                const itemQuestionWords = item.customerQuestion.toLowerCase().split(' ');

                questionWords.forEach(word => {
                    if (itemQuestionWords.includes(word) && word.length > 3) {
                        score += 1; // Question similarity
                    }
                });

                if (score > 0) {
                    suggestions.push({
                        ...item,
                        score: score,
                        index: index,
                        matchedKeywords: matchedKeywords,
                        confidence: Math.min(Math.round((score / questionKeywords.length) * 100), 100)
                    });
                }
            });

            // Sort by relevance
            suggestions.sort((a, b) => b.score - a.score);

            if (suggestions.length > 0) {
                displaySuggestions(suggestions);
                document.getElementById('noMatchSection').style.display = 'none';
            } else {
                document.getElementById('suggestionsSection').style.display = 'none';
                document.getElementById('noMatchSection').style.display = 'block';
                document.getElementById('noMatchKeywords').textContent = questionKeywords.join(', ');
            }
        }

        // Display suggestions with confidence scores
        function displaySuggestions(suggestions) {
            const suggestionsSection = document.getElementById('suggestionsSection');
            const suggestionsList = document.getElementById('suggestionsList');

            suggestionsList.innerHTML = suggestions.map(suggestion => `
                <div class="suggestion-item" onclick="selectSuggestion('${suggestion.index}')">
                    <div class="suggestion-title">
                        📝 ${suggestion.customerQuestion}
                        <span class="confidence-score">${suggestion.confidence}% match</span>
                    </div>
                    <div style="color: #666; font-size: 12px; margin: 5px 0;">
                        <strong>Matched keywords:</strong> ${suggestion.matchedKeywords.join(', ')}
                    </div>
                    <div style="color: #666; font-size: 14px;">${suggestion.answer.substring(0, 100)}...</div>
                </div>
            `).join('');

            suggestionsSection.style.display = 'block';
        }

        // Select suggestion
        function selectSuggestion(index) {
            const knowledge = knowledgeBase[currentOutlet];
            const response = knowledge[index].answer;
            document.getElementById('responseArea').textContent = response;
        }

        // Extract keywords from new question
        function extractKeywordsFromNew() {
            const question = document.getElementById('newCustomerQuestion').value;

            if (!question.trim()) {
                alert('Please enter a customer question first!');
                return;
            }

            const keywords = smartExtractKeywords(question);
            document.getElementById('newKeywords').value = keywords.join(', ');
        }

        // Add new knowledge
        function addKnowledge() {
            const outlet = document.getElementById('newOutlet').value;
            const customerQuestion = document.getElementById('newCustomerQuestion').value;
            const keywordsText = document.getElementById('newKeywords').value;
            const answer = document.getElementById('newAnswer').value;

            if (!outlet || !customerQuestion || !keywordsText || !answer) {
                alert('Please fill in all fields!');
                return;
            }

            const keywords = keywordsText.split(',').map(k => k.trim().toLowerCase()).filter(k => k);

            if (!knowledgeBase[outlet]) {
                knowledgeBase[outlet] = [];
            }

            knowledgeBase[outlet].push({
                keywords: keywords,
                customerQuestion: customerQuestion,
                answer: answer,
                dateAdded: new Date().toISOString().split('T')[0]
            });

            saveKnowledgeBase();
            displayKnowledgeBase();

            // Clear form
            document.getElementById('newOutlet').value = '';
            document.getElementById('newCustomerQuestion').value = '';
            document.getElementById('newKeywords').value = '';
            document.getElementById('newAnswer').value = '';

            alert('Knowledge added successfully! The system is now smarter.');
        }

        // Update statistics
        function updateStats() {
            const trappedCount = knowledgeBase.trapped ? knowledgeBase.trapped.length : 0;
            const triggeredCount = knowledgeBase.triggered ? knowledgeBase.triggered.length : 0;

            let totalKeywords = 0;
            Object.values(knowledgeBase).forEach(outlet => {
                outlet.forEach(item => {
                    totalKeywords += item.keywords.length;
                });
            });

            document.getElementById('trappedCount').textContent = trappedCount;
            document.getElementById('triggeredCount').textContent = triggeredCount;
            document.getElementById('totalKeywords').textContent = totalKeywords;
        }

        // Display knowledge base
        function displayKnowledgeBase() {
            const knowledgeList = document.getElementById('knowledgeList');
            const filter = document.getElementById('filterOutlet')?.value || '';

            let html = '';

            Object.keys(knowledgeBase).forEach(outlet => {
                if (filter && filter !== outlet) return;

                knowledgeBase[outlet].forEach((item, index) => {
                    html += `
                        <div class="knowledge-item">
                            <div class="knowledge-header">
                                <strong>${outlet.toUpperCase()}: ${item.customerQuestion}</strong>
                                <button class="delete-btn" onclick="deleteKnowledge('${outlet}', ${index})">🗑️ Delete</button>
                            </div>
                            <div style="margin: 10px 0;">
                                <strong>Keywords:</strong> ${item.keywords.join(', ')}
                            </div>
                            <div style="margin: 10px 0;">
                                <strong>Answer:</strong> ${item.answer.substring(0, 200)}...
                            </div>
                            ${item.dateAdded ? `<div style="font-size: 12px; color: #666;">Added: ${item.dateAdded}</div>` : ''}
                        </div>
                    `;
                });
            });

            knowledgeList.innerHTML = html || '<p>No knowledge entries found.</p>';
        }

        // Delete knowledge
        function deleteKnowledge(outlet, index) {
            if (confirm('Are you sure you want to delete this knowledge entry?')) {
                knowledgeBase[outlet].splice(index, 1);
                saveKnowledgeBase();
                displayKnowledgeBase();
            }
        }

        // Filter knowledge
        function filterKnowledge() {
            displayKnowledgeBase();
        }

        // Export knowledge
        function exportKnowledge() {
            const dataStr = JSON.stringify(knowledgeBase, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = 'adventure_entertainment_knowledge_base.json';
            link.click();
        }

        // Import knowledge
        function importKnowledge() {
            const file = document.getElementById('importFile').files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const imported = JSON.parse(e.target.result);
                    knowledgeBase = imported;
                    saveKnowledgeBase();
                    displayKnowledgeBase();
                    alert('Knowledge base imported successfully!');
                } catch (error) {
                    alert('Error importing file. Please check the format.');
                }
            };
            reader.readAsText(file);
        }

        // Copy response
        function copyResponse() {
            const responseText = document.getElementById('responseArea').textContent;
            navigator.clipboard.writeText(responseText).then(() => {
                document.getElementById('copySuccess').style.display = 'block';
                setTimeout(() => {
                    document.getElementById('copySuccess').style.display = 'none';
                }, 3000);
            });
        }

        // Price calculator
        function calculatePrice() {
            const outlet = document.getElementById('calcOutlet').value;
            const participants = parseInt(document.getElementById('calcParticipants').value);
            const timing = document.getElementById('calcTiming').value;
            const games = parseInt(document.getElementById('calcGames').value);

            if (!outlet || !participants || !timing || !games) {
                alert('Please fill in all fields');
                return;
            }

            let basePrice = 0;
            let gstNote = '';

            // Accurate pricing
            if (outlet === 'triggered') {
                if (timing === 'off-peak') {
                    if (participants >= 40) basePrice = 13.60;
                    else if (participants >= 20) basePrice = 14.40;
                    else basePrice = 16.00;
                } else {
                    if (participants >= 40) basePrice = 17.00;
                    else if (participants >= 20) basePrice = 18.00;
                    else basePrice = 20.00;
                }
                gstNote = '(GST included)';
            } else {
                if (timing === 'off-peak') {
                    if (participants >= 40) basePrice = 26.90;
                    else if (participants >= 20) basePrice = 27.90;
                    else basePrice = 28.90;
                } else {
                    if (participants >= 40) basePrice = 32.90;
                    else if (participants >= 20) basePrice = 33.90;
                    else basePrice = 34.90;
                }
                gstNote = '(GST not included)';
            }

            const totalPrice = participants * basePrice * games;
            const deposit = totalPrice * 0.5;

            const quoteHtml = `
                <strong>${outlet.toUpperCase()} - ${timing.toUpperCase()}</strong><br>
                ${participants} participants × ${games} games × $${basePrice} = $${totalPrice.toFixed(2)}<br>
                <strong>TOTAL: $${totalPrice.toFixed(2)} ${gstNote}</strong><br>
                <em>Deposit required: $${deposit.toFixed(2)} (50%)</em>
            `;

            document.getElementById('quoteDetails').innerHTML = quoteHtml;
            document.getElementById('calcResult').style.display = 'block';
        }

        // Initialize
        window.onload = function() {
            loadKnowledgeBase();
        };
    </script>
</body>
</html>
