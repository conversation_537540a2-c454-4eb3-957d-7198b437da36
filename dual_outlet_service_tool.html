<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Adventure Entertainment - Dual Outlet Service Tool</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .main-content {
            padding: 30px;
        }
        
        .outlet-selector {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .outlet-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        
        .outlet-btn {
            padding: 20px;
            border: 3px solid #ddd;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }
        
        .outlet-btn.active {
            border-color: #667eea;
            background: #f0f4ff;
        }
        
        .outlet-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .outlet-title {
            font-size: 1.5em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .outlet-desc {
            color: #666;
            font-size: 14px;
        }
        
        .trapped-theme {
            border-color: #6f42c1 !important;
            background: #f8f4ff !important;
        }
        
        .triggered-theme {
            border-color: #28a745 !important;
            background: #f0fff4 !important;
        }
        
        .question-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 30px;
            display: none;
        }
        
        .question-textarea {
            width: 100%;
            height: 120px;
            padding: 15px;
            font-size: 16px;
            border: 2px solid #ddd;
            border-radius: 8px;
            margin-bottom: 15px;
            resize: vertical;
        }
        
        .analyze-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
        }
        
        .suggestions-section {
            background: #e8f5e8;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 30px;
            display: none;
        }
        
        .suggestion-item {
            background: white;
            padding: 15px;
            margin-bottom: 15px;
            border-radius: 8px;
            border-left: 4px solid #28a745;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .suggestion-item:hover {
            transform: translateX(5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .suggestion-title {
            font-weight: bold;
            color: #28a745;
            margin-bottom: 5px;
        }
        
        .response-area {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            min-height: 200px;
            font-family: monospace;
            white-space: pre-wrap;
            display: none;
        }
        
        .copy-btn {
            background: #17a2b8;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 15px;
            font-size: 16px;
        }
        
        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            display: none;
        }
        
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
            display: none;
        }
        
        .quick-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .quick-btn:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        
        .outlet-info {
            background: #fff3cd;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #ffc107;
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Dual Outlet Service Tool</h1>
            <p>Adventure Entertainment Singapore - Separate Systems for Each Outlet</p>
        </div>
        
        <div class="main-content">
            <!-- OUTLET SELECTION -->
            <div class="outlet-selector">
                <h3>🏢 First, Select Which Outlet the Customer is Asking About:</h3>
                
                <div class="outlet-buttons">
                    <div class="outlet-btn" onclick="selectOutlet('trapped')">
                        <div class="outlet-title">🏠 TRAPPED ESCAPE ROOM</div>
                        <div class="outlet-desc">
                            • 75-minute escape room experiences<br>
                            • 7 themed rooms (thriller, sci-fi, supernatural)<br>
                            • Mental puzzles and problem-solving<br>
                            • Prices exclude GST
                        </div>
                    </div>
                    
                    <div class="outlet-btn" onclick="selectOutlet('triggered')">
                        <div class="outlet-title">🎮 TRIGGERED GAMES</div>
                        <div class="outlet-desc">
                            • 20-minute interactive physical games<br>
                            • 6 high-energy challenge rooms<br>
                            • Team building and physical activity<br>
                            • Prices include GST
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- OUTLET-SPECIFIC INFO -->
            <div id="trapped-info" class="outlet-info">
                <h4>🏠 TRAPPED ESCAPE ROOM - Current Selection</h4>
                <p><strong>Games:</strong> The Cursed Video Tape, The Strangest Things, The Great Escape, Hostel Warlock, The Attic, The Asylum, The Alchemist's Cabinet</p>
                <p><strong>Duration:</strong> 75 minutes each | <strong>Pricing:</strong> Excludes GST | <strong>Early Bird:</strong> Available (15+ people, 3 months advance)</p>
            </div>
            
            <div id="triggered-info" class="outlet-info">
                <h4>🎮 TRIGGERED GAMES - Current Selection</h4>
                <p><strong>Games:</strong> Floor is Lava (2 rooms), Press It!, Hoops Madness, Hide & Seek, Hexa Blasts</p>
                <p><strong>Duration:</strong> 20 minutes each | <strong>Pricing:</strong> Includes GST | <strong>Capacity:</strong> Max 46 people total</p>
            </div>
            
            <!-- QUICK ACTIONS -->
            <div id="quick-actions" class="quick-actions">
                <button class="quick-btn" onclick="showQuickResponse('pricing')">💰 Pricing Info</button>
                <button class="quick-btn" onclick="showQuickResponse('games')">🎮 Game Details</button>
                <button class="quick-btn" onclick="showQuickResponse('booking')">📅 Booking Process</button>
                <button class="quick-btn" onclick="showQuickResponse('policies')">📋 Policies</button>
            </div>
            
            <!-- QUESTION ANALYSIS -->
            <div id="question-section" class="question-section">
                <h3>📝 Paste Customer Question Here</h3>
                <textarea class="question-textarea" id="customerQuestion" placeholder="Paste the customer's email or question here...

Example:
'Hi, we want to book escape rooms for 20 people for team building. What are your rates?'"></textarea>
                
                <button class="analyze-btn" onclick="analyzeQuestion()">🔍 Find Best Response</button>
            </div>
            
            <!-- SUGGESTIONS -->
            <div id="suggestions-section" class="suggestions-section">
                <h3>💡 Suggested Responses</h3>
                <div id="suggestions-list"></div>
            </div>
            
            <!-- RESPONSE AREA -->
            <div id="response-area" class="response-area">
                Select an outlet above to get started!
            </div>
            
            <button class="copy-btn" onclick="copyResponse()" style="display: none;" id="copy-button">📋 Copy Response</button>
            <div class="success-message" id="copy-success">✅ Response copied to clipboard!</div>
        </div>
    </div>

    <script>
        let currentOutlet = null;
        
        // Outlet-specific knowledge bases
        const trappedKnowledge = [
            {
                keywords: ['price', 'pricing', 'cost', 'rate', 'how much'],
                question: 'What are your escape room prices?',
                answer: `Hi [Customer Name],

Thank you for your interest in our escape room experiences!

🏠 **TRAPPED ESCAPE ROOM PRICING (75 mins each)**

**OFF-PEAK (Mon-Fri before 6pm):**
• 1-19 people: $28.90 per game per person
• 20-39 people: $27.90 per game per person  
• 40+ people: $26.90 per game per person

**PEAK (Weekdays after 6pm, weekends & holidays):**
• 1-19 people: $34.90 per game per person
• 20-39 people: $33.90 per game per person
• 40+ people: $32.90 per game per person

**EARLY BIRD SPECIAL:**
Additional $3 discount per game per person if deposit paid 3 months in advance (minimum 15 people)

*All prices exclude GST*

📦 **PACKAGES AVAILABLE:**
• Package A: 1 escape game per person
• Package B: 2 escape games per person
• Package C: 3 escape games per person

To provide an accurate quote, please let me know:
1. Number of participants
2. Preferred date and time
3. How many games per person

Best regards,
[Your Name]
Adventure Entertainment Singapore`
            },
            {
                keywords: ['games', 'rooms', 'themes', 'what games', 'activities'],
                question: 'What escape room games do you have?',
                answer: `Hi [Customer Name],

We have 7 exciting escape room experiences (75 minutes each):

🎬 **THE CURSED VIDEO TAPE** - Thriller
Suspenseful and gritty with live actor involvement. Perfect for thrill-seekers!

🔬 **THE STRANGEST THINGS** - Science Fiction  
Mystery-focused sci-fi adventure. Great for puzzle enthusiasts!

🏛️ **THE GREAT ESCAPE** - Prison Theme
Witty and fun with brief live actor scenes. Classic escape experience!

👻 **HOSTEL WARLOCK** - Supernatural
Investigative mystery with supernatural elements. Atmospheric and engaging!

🏠 **THE ATTIC** - Kidnapped Theme
Thriller and mystery with suspenseful elements. Heart-pumping adventure!

🏥 **THE ASYLUM** - Investigative
Mystery and suspense-focused. Perfect for detective enthusiasts!

🔮 **THE ALCHEMIST'S CABINET** - Magic Theme
NO scary elements! Family-friendly magical adventure perfect for those who dislike horror.

Each room can accommodate different group sizes. Which themes interest your group most?

Best regards,
[Your Name]
Adventure Entertainment Singapore`
            }
        ];
        
        const triggeredKnowledge = [
            {
                keywords: ['price', 'pricing', 'cost', 'rate', 'how much'],
                question: 'What are your Triggered Games prices?',
                answer: `Hi [Customer Name],

Thank you for your interest in our high-energy interactive games!

🎮 **TRIGGERED GAMES PRICING (20 mins each)**

**OFF-PEAK (Mon-Thu all day, Fri before 6pm):**
• 1-19 people: $16.00 per game per person
• 20-39 people: $14.40 per game per person
• 40+ people: $13.60 per game per person

**PEAK (Fri after 6pm, weekends & holidays):**
• 1-19 people: $20.00 per game per person
• 20-39 people: $18.00 per game per person
• 40+ people: $17.00 per game per person

*All prices include GST*

📦 **PACKAGES AVAILABLE:**
• Package A: 1 game per person
• Package B: 2 games per person
• Package C: 3 games per person
• Package D: 4 games per person
• Package E: 5 games per person

**CAPACITY:** Maximum 46 participants across all game rooms

To provide an accurate quote, please let me know:
1. Number of participants
2. Preferred date and time
3. How many games per person

Best regards,
[Your Name]
Adventure Entertainment Singapore`
            },
            {
                keywords: ['games', 'activities', 'what games', 'rooms'],
                question: 'What Triggered Games do you have?',
                answer: `Hi [Customer Name],

We have 6 high-energy interactive games (20 minutes each):

🌋 **FLOOR IS LAVA (Rooms 1 & 2)** - Max 10 people each
Navigate green safe zones, avoid red lava zones, tap blue grids to complete levels!

🔘 **PRESS IT!** - Max 8 people
Memory challenge! Watch the light patterns, press buttons in correct sequence. Wrong button = lost points!

🏀 **HOOPS MADNESS** - Max 10 people  
Throw balls into illuminated hoops, but only certain colors count. Red hoops deduct points!

👁️ **HIDE & SEEK** - Max 8 people
Complete missions while avoiding the devil's eye! Hide behind the center pillar when spotted!

⬡ **HEXA BLASTS** - Max 10 people
Hit hexagon lights with balls following the pattern. Wrong hexagon = point deduction!

🏆 **PERFECT FOR:**
• Corporate team building
• High-energy group activities
• Strategic thinking + physical activity
• Competitive team challenges

**Total capacity:** 46 participants maximum across all rooms

Which games sound most exciting for your team?

Best regards,
[Your Name]
Adventure Entertainment Singapore`
            }
        ];
        
        // Select outlet
        function selectOutlet(outlet) {
            currentOutlet = outlet;
            
            // Update UI
            document.querySelectorAll('.outlet-btn').forEach(btn => {
                btn.classList.remove('active', 'trapped-theme', 'triggered-theme');
            });
            
            event.target.closest('.outlet-btn').classList.add('active');
            
            if (outlet === 'trapped') {
                event.target.closest('.outlet-btn').classList.add('trapped-theme');
                document.getElementById('trapped-info').style.display = 'block';
                document.getElementById('triggered-info').style.display = 'none';
            } else {
                event.target.closest('.outlet-btn').classList.add('triggered-theme');
                document.getElementById('triggered-info').style.display = 'block';
                document.getElementById('trapped-info').style.display = 'none';
            }
            
            // Show sections
            document.getElementById('quick-actions').style.display = 'grid';
            document.getElementById('question-section').style.display = 'block';
            document.getElementById('response-area').style.display = 'block';
            document.getElementById('copy-button').style.display = 'inline-block';
            
            // Update response area
            document.getElementById('response-area').textContent = `${outlet.toUpperCase()} selected! Use the buttons above or paste a customer question below.`;
        }
        
        // Show quick responses
        function showQuickResponse(type) {
            if (!currentOutlet) return;
            
            const knowledge = currentOutlet === 'trapped' ? trappedKnowledge : triggeredKnowledge;
            let response = '';
            
            if (type === 'pricing') {
                response = knowledge.find(item => item.keywords.includes('price'))?.answer || 'Pricing info not found';
            } else if (type === 'games') {
                response = knowledge.find(item => item.keywords.includes('games'))?.answer || 'Games info not found';
            }
            
            document.getElementById('response-area').textContent = response;
        }
        
        // Analyze question
        function analyzeQuestion() {
            if (!currentOutlet) {
                alert('Please select an outlet first!');
                return;
            }
            
            const question = document.getElementById('customerQuestion').value.toLowerCase();
            
            if (!question.trim()) {
                alert('Please paste a customer question first!');
                return;
            }
            
            const knowledge = currentOutlet === 'trapped' ? trappedKnowledge : triggeredKnowledge;
            const suggestions = [];
            
            // Find matching responses
            knowledge.forEach((item, index) => {
                let score = 0;
                item.keywords.forEach(keyword => {
                    if (question.includes(keyword)) {
                        score++;
                    }
                });
                
                if (score > 0) {
                    suggestions.push({
                        ...item,
                        score: score,
                        index: index
                    });
                }
            });
            
            // Sort by relevance
            suggestions.sort((a, b) => b.score - a.score);
            
            displaySuggestions(suggestions);
        }
        
        // Display suggestions
        function displaySuggestions(suggestions) {
            const suggestionsSection = document.getElementById('suggestions-section');
            const suggestionsList = document.getElementById('suggestions-list');
            
            if (suggestions.length === 0) {
                suggestionsList.innerHTML = '<p>No matching responses found for this outlet. Try the quick action buttons above!</p>';
            } else {
                suggestionsList.innerHTML = suggestions.map(suggestion => `
                    <div class="suggestion-item" onclick="selectSuggestion('${suggestion.index}')">
                        <div class="suggestion-title">📝 ${suggestion.question}</div>
                        <div style="color: #666; font-size: 14px;">${suggestion.answer.substring(0, 100)}...</div>
                    </div>
                `).join('');
            }
            
            suggestionsSection.style.display = 'block';
        }
        
        // Select suggestion
        function selectSuggestion(index) {
            const knowledge = currentOutlet === 'trapped' ? trappedKnowledge : triggeredKnowledge;
            const response = knowledge[index].answer;
            document.getElementById('response-area').textContent = response;
        }
        
        // Copy response
        function copyResponse() {
            const responseText = document.getElementById('response-area').textContent;
            navigator.clipboard.writeText(responseText).then(() => {
                document.getElementById('copy-success').style.display = 'block';
                setTimeout(() => {
                    document.getElementById('copy-success').style.display = 'none';
                }, 3000);
            });
        }
    </script>
</body>
</html>
