<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Adventure Entertainment - Interactive Q&A System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .controls {
            padding: 20px 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            align-items: center;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0056b3;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #1e7e34;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-warning:hover {
            background: #e0a800;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .main-content {
            padding: 30px;
        }
        
        .qa-item {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            margin-bottom: 20px;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .qa-item:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .qa-header {
            background: #e9ecef;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
        }
        
        .qa-header:hover {
            background: #dee2e6;
        }
        
        .question {
            font-weight: 600;
            color: #495057;
            flex: 1;
        }
        
        .qa-controls {
            display: flex;
            gap: 10px;
        }
        
        .btn-sm {
            padding: 5px 10px;
            font-size: 0.875em;
        }
        
        .qa-content {
            padding: 20px;
            display: none;
        }
        
        .qa-content.active {
            display: block;
        }
        
        .answer-display {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 15px;
            white-space: pre-wrap;
            line-height: 1.6;
        }
        
        .answer-edit {
            width: 100%;
            min-height: 150px;
            padding: 15px;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            font-family: inherit;
            font-size: 14px;
            line-height: 1.6;
            resize: vertical;
        }
        
        .edit-controls {
            margin-top: 10px;
            display: flex;
            gap: 10px;
        }
        
        .category-filter {
            margin-bottom: 20px;
        }
        
        .category-filter select {
            padding: 8px 12px;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .stats {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: flex;
            gap: 30px;
            flex-wrap: wrap;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 1.5em;
            font-weight: bold;
            color: #1976d2;
        }
        
        .stat-label {
            font-size: 0.875em;
            color: #666;
        }
        
        .search-box {
            width: 300px;
            padding: 8px 12px;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .export-section {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
        }
        
        .hidden {
            display: none;
        }
        
        .collapse-icon {
            transition: transform 0.3s ease;
        }
        
        .collapse-icon.rotated {
            transform: rotate(180deg);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Interactive Q&A System</h1>
            <p>Automatically generated questions with editable answers</p>
        </div>
        
        <div class="controls">
            <button class="btn btn-primary" onclick="generateNewQA()">🔄 Generate New Q&A</button>
            <button class="btn btn-success" onclick="addCustomQA()">➕ Add Custom Q&A</button>
            <button class="btn btn-warning" onclick="exportQA()">📤 Export All Q&A</button>
            <input type="text" class="search-box" placeholder="Search questions..." onkeyup="searchQA(this.value)">
            <div class="category-filter">
                <select onchange="filterByCategory(this.value)">
                    <option value="">All Categories</option>
                    <option value="general">General Info</option>
                    <option value="pricing">Pricing</option>
                    <option value="booking">Booking</option>
                    <option value="games">Games</option>
                    <option value="policies">Policies</option>
                </select>
            </div>
        </div>
        
        <div class="main-content">
            <div class="stats">
                <div class="stat-item">
                    <div class="stat-number" id="totalQA">0</div>
                    <div class="stat-label">Total Q&A</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="editedQA">0</div>
                    <div class="stat-label">Edited</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="categoriesCount">0</div>
                    <div class="stat-label">Categories</div>
                </div>
            </div>
            
            <div id="qaContainer">
                <!-- Q&A items will be dynamically generated here -->
            </div>
            
            <div class="export-section hidden" id="exportSection">
                <h4>📋 Export Data</h4>
                <textarea id="exportData" readonly style="width: 100%; height: 200px; margin-top: 10px;"></textarea>
                <div style="margin-top: 10px;">
                    <button class="btn btn-primary" onclick="copyToClipboard()">📋 Copy to Clipboard</button>
                    <button class="btn btn-secondary" onclick="downloadJSON()">💾 Download JSON</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let qaData = [];
        let editedAnswers = new Set();
        
        // Pre-generated Q&A based on your business content
        const defaultQAData = [
            {
                id: 1,
                category: 'general',
                question: 'What is the difference between Trapped and Triggered?',
                answer: `Great question! We operate two different entertainment outlets:

🏠 **TRAPPED ESCAPE ROOM**
- Traditional escape room experiences (75 minutes each)
- 7 themed rooms: thriller, sci-fi, supernatural, etc.
- Mental puzzles and problem-solving focus
- Perfect for those who enjoy mystery and investigation

🎮 **TRIGGERED GAMES** 
- Interactive physical challenge games (20 minutes each)
- 6 high-energy rooms: Floor is Lava, Press It!, Hoops Madness, etc.
- Physical activity combined with strategy
- Great for team building and active groups

Both venues are located at Cathay Cineleisure Orchard, about 6 minutes walking distance from each other.`
            },
            {
                id: 2,
                category: 'general',
                question: 'What are your location and operating hours?',
                answer: `📍 **ADDRESS:**
8 Grange Road, #02-08 
Cathay Cineleisure Orchard
Singapore 239695

🕐 **OPERATING HOURS:**
Daily from 11am - 10pm

🚗 **GETTING THERE:**
- MRT: Orchard Station (5-min walk)
- Ample parking available at Cathay Cineleisure
- Central location with restaurants and amenities nearby`
            }
        ];

        // Additional pre-generated Q&A
        const moreQAData = [
            {
                id: 3,
                category: 'pricing',
                question: 'What are your pricing packages and rates?',
                answer: `🏠 **TRAPPED ESCAPE ROOM (75 mins each)**
Off-peak (Mon-Fri before 6pm): $28.90 per game
Peak (Evenings/Weekends): $34.90 per game
*Prices exclude GST

🎮 **TRIGGERED GAMES (20 mins each)**
Off-peak (Mon-Thu all day, Fri before 6pm): $16.00 per game
Peak (Fri after 6pm, weekends): $20.00 per game
*Prices include GST

📦 **PACKAGES AVAILABLE:**
- Package A: 1 game per person
- Package B: 2 games per person
- Package C: 3 games per person
- (Triggered also has Packages D & E for 4-5 games)

🎯 **MYSTERY HUNT ADD-ON:** $20 per person (any package)`
            },
            {
                id: 4,
                category: 'pricing',
                question: 'Do you offer group discounts?',
                answer: `Yes, we offer attractive group discounts!

🏠 **TRAPPED ESCAPE ROOM:**
- 1-19 pax: Standard rate
- 20-39 pax: $1 discount per game per person
- 40+ pax: $2 discount per game per person

🎮 **TRIGGERED GAMES:**
- 1-19 pax: Standard rate
- 20-39 pax: 10% discount per game per person
- 40+ pax: 15% discount per game per person

💰 **ADDITIONAL SAVINGS:**
- Early bird discount (Trapped only): Extra $3 off per game if deposit paid 3 months in advance`
            },
            {
                id: 5,
                category: 'booking',
                question: 'How do I make a booking?',
                answer: `📋 **STEP-BY-STEP BOOKING:**
1. **Choose your outlet:** Trapped or Triggered
2. **Select participants:** How many people
3. **Pick your package:** Number of games per person
4. **Choose date & time:** We'll confirm availability
5. **Add Mystery Hunt:** Optional $20/person add-on
6. **Receive quote:** Total pricing with any discounts
7. **Pay deposit:** 50% to secure your slot
8. **Final payment:** Balance on event day

💳 **PAYMENT METHODS:**
- Bank transfer, Credit card (Visa, Master, Amex)
- PayNow UEN, Cash (on event day)
- VENDORS.GOV.SG / GEBIZ (for government bookings)

⏰ **IMPORTANT:** Confirm booking within 3 working days`
            },
            {
                id: 6,
                category: 'games',
                question: 'What Trapped escape room games do you have?',
                answer: `🎬 **THE CURSED VIDEO TAPE** - Thriller
Suspenseful and gritty with live actor involvement

🔬 **THE STRANGEST THINGS** - Science Fiction
Mystery-focused sci-fi adventure

🏛️ **THE GREAT ESCAPE** - Prison Theme
Witty and fun with brief live actor scenes

👻 **HOSTEL WARLOCK** - Supernatural
Investigative mystery with supernatural elements

🏠 **THE ATTIC** - Kidnapped Theme
Thriller and mystery with suspenseful elements

🏥 **THE ASYLUM** - Investigative
Mystery and suspense-focused

🔮 **THE ALCHEMIST'S CABINET** - Magic Theme
NO scary elements! Family-friendly magical adventure

Each game is 75 minutes long and accommodates different group sizes.`
            },
            {
                id: 7,
                category: 'games',
                question: 'What are the Triggered Games available?',
                answer: `🌋 **FLOOR IS LAVA (Rooms 1 & 2)** - Max 10 pax each
Navigate green safe zones, avoid red lava zones, tap blue grids to complete levels!

🔘 **PRESS IT!** - Max 8 pax
Memory challenge! Watch light patterns, press buttons in correct sequence

🏀 **HOOPS MADNESS** - Max 10 pax
Throw balls into illuminated hoops, but only certain colors count

👁️ **HIDE & SEEK** - Max 8 pax
Complete missions while avoiding the devil's eye!

⬡ **HEXA BLASTS** - Max 10 pax
Hit hexagon lights with balls following the pattern

**Total capacity:** 46 participants across all rooms
**Duration:** 20 minutes each game`
            },
            {
                id: 8,
                category: 'policies',
                question: 'What is your cancellation policy?',
                answer: `❌ **CANCELLATION TERMS:**
- All deposits are strictly non-refundable
- No exceptions for any reason (weather, illness, etc.)

🔄 **ALTERNATIVE OPTIONS:**
- **One-time rescheduling** available (subject to availability)
- Must notify us at least 1 week in advance
- Only one rescheduling permitted per booking

⚠️ **IMPORTANT NOTES:**
- Changes less than 1 week before event = deposit forfeiture
- No-shows will still be charged full amount
- Additional participants on event day = revised invoice`
            },
            {
                id: 9,
                category: 'general',
                question: 'Are there any age restrictions or accessibility concerns?',
                answer: `👥 **AGE REQUIREMENTS:**
- No specific age restrictions
- Children should be accompanied by adults
- Games are designed for teens and adults

♿ **ACCESSIBILITY:**
- Unfortunately, our venues are not wheelchair accessible
- Triggered Games involve running, jumping, and reaching high places
- Not suitable for pregnant guests at any stage due to physical activity requirements

🏥 **ALTERNATIVE FOR MOBILITY CONCERNS:**
For pregnant guests or those with mobility issues, we highly recommend our Trapped Escape Room activities instead, as they focus on mental challenges rather than physical activity.`
            },
            {
                id: 10,
                category: 'games',
                question: 'What is the Mystery Hunt add-on?',
                answer: `🕵️ **MYSTERY HUNT DETAILS:**
- **Price:** $20 per person (fixed rate, no discounts)
- **Duration:** Flexible timing
- **Location:** Around Cathay Cineleisure mall
- **Weather:** Rain or shine event

🎯 **WHAT'S INCLUDED:**
- Teams travel around the mall completing challenges
- GPS coordinate treasure hunts
- Photo landmark identification
- TikTok-style video creation challenges
- Team leader and timekeeper roles

💡 **PERFECT FOR:**
- Large groups (40+ participants)
- Overflow when Triggered Games is full
- Competition between multiple teams
- Ice-breaker activities`
            }
        ];

        // Initialize the system
        function initializeQA() {
            qaData = [...defaultQAData, ...moreQAData];
            renderQA();
            updateStats();
        }

        function renderQA(filteredData = null) {
            const container = document.getElementById('qaContainer');
            const dataToRender = filteredData || qaData;

            container.innerHTML = dataToRender.map(qa => `
                <div class="qa-item" data-category="${qa.category}" data-id="${qa.id}">
                    <div class="qa-header" onclick="toggleQA(${qa.id})">
                        <div class="question">${qa.question}</div>
                        <div class="qa-controls">
                            <span class="collapse-icon" id="icon-${qa.id}">▼</span>
                            <button class="btn btn-sm btn-warning" onclick="event.stopPropagation(); editAnswer(${qa.id})">✏️ Edit</button>
                            <button class="btn btn-sm btn-danger" onclick="event.stopPropagation(); deleteQA(${qa.id})">🗑️ Delete</button>
                        </div>
                    </div>
                    <div class="qa-content" id="content-${qa.id}">
                        <div class="answer-display" id="answer-${qa.id}">${qa.answer}</div>
                        <div class="hidden" id="edit-${qa.id}">
                            <textarea class="answer-edit" id="textarea-${qa.id}">${qa.answer}</textarea>
                            <div class="edit-controls">
                                <button class="btn btn-success btn-sm" onclick="saveAnswer(${qa.id})">💾 Save</button>
                                <button class="btn btn-secondary btn-sm" onclick="cancelEdit(${qa.id})">❌ Cancel</button>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        function toggleQA(id) {
            const content = document.getElementById(`content-${id}`);
            const icon = document.getElementById(`icon-${id}`);

            if (content.classList.contains('active')) {
                content.classList.remove('active');
                icon.textContent = '▼';
                icon.classList.remove('rotated');
            } else {
                content.classList.add('active');
                icon.textContent = '▲';
                icon.classList.add('rotated');
            }
        }

        function editAnswer(id) {
            const answerDiv = document.getElementById(`answer-${id}`);
            const editDiv = document.getElementById(`edit-${id}`);

            answerDiv.classList.add('hidden');
            editDiv.classList.remove('hidden');
        }

        function saveAnswer(id) {
            const textarea = document.getElementById(`textarea-${id}`);
            const newAnswer = textarea.value;

            // Update the data
            const qaItem = qaData.find(qa => qa.id === id);
            if (qaItem) {
                qaItem.answer = newAnswer;
                editedAnswers.add(id);
            }

            // Update the display
            document.getElementById(`answer-${id}`).textContent = newAnswer;
            cancelEdit(id);
            updateStats();
        }

        function cancelEdit(id) {
            const answerDiv = document.getElementById(`answer-${id}`);
            const editDiv = document.getElementById(`edit-${id}`);

            answerDiv.classList.remove('hidden');
            editDiv.classList.add('hidden');
        }

        function deleteQA(id) {
            if (confirm('Are you sure you want to delete this Q&A?')) {
                qaData = qaData.filter(qa => qa.id !== id);
                editedAnswers.delete(id);
                renderQA();
                updateStats();
            }
        }

        function generateNewQA() {
            const newQuestions = [
                {
                    category: 'booking',
                    question: 'What information do you need for booking?',
                    answer: `**INFORMATION WE NEED:**
- Contact person name & mobile
- Organization name & billing address
- 1st and 2nd contact persons
- Number of participants
- Preferred date and time
- Which outlet (Trapped or Triggered)
- Package preference (number of games)`
                },
                {
                    category: 'policies',
                    question: 'Can I change the number of participants after booking?',
                    answer: `📊 **GROUP SIZE POLICY:**
- Changes allowed up to 1 week before event
- Numbers locked 1 week before event date
- No further changes after lock-in period

💰 **BILLING ADJUSTMENTS:**
- Increase in participants = additional charges
- Decrease in participants = no refund
- No-shows still charged full amount
- Additional participants on event day = revised invoice`
                },
                {
                    category: 'general',
                    question: 'What should participants wear?',
                    answer: `👕 **DRESS CODE:**
- Comfortable clothing recommended
- Covered footwear mandatory (no sandals, heels, crocs or slippers allowed)
- Suitable for physical activity (especially for Triggered Games)

📝 **PRE-EVENT REQUIREMENTS:**
- Arrive 10-15 minutes early
- All participants must sign waiver forms
- Organize team groupings before arrival`
                }
            ];

            const randomQuestion = newQuestions[Math.floor(Math.random() * newQuestions.length)];
            const newId = Math.max(...qaData.map(qa => qa.id)) + 1;

            qaData.push({
                id: newId,
                ...randomQuestion
            });

            renderQA();
            updateStats();
            alert('New Q&A generated! Check the bottom of the list.');
        }

        function addCustomQA() {
            const question = prompt('Enter your question:');
            if (!question) return;

            const answer = prompt('Enter the answer:');
            if (!answer) return;

            const category = prompt('Enter category (general/pricing/booking/games/policies):') || 'general';

            const newId = Math.max(...qaData.map(qa => qa.id)) + 1;

            qaData.push({
                id: newId,
                category: category,
                question: question,
                answer: answer
            });

            renderQA();
            updateStats();
        }

        function searchQA(searchTerm) {
            if (!searchTerm.trim()) {
                renderQA();
                return;
            }

            const filtered = qaData.filter(qa =>
                qa.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
                qa.answer.toLowerCase().includes(searchTerm.toLowerCase())
            );

            renderQA(filtered);
        }

        function filterByCategory(category) {
            if (!category) {
                renderQA();
                return;
            }

            const filtered = qaData.filter(qa => qa.category === category);
            renderQA(filtered);
        }

        function updateStats() {
            document.getElementById('totalQA').textContent = qaData.length;
            document.getElementById('editedQA').textContent = editedAnswers.size;

            const categories = [...new Set(qaData.map(qa => qa.category))];
            document.getElementById('categoriesCount').textContent = categories.length;
        }

        function exportQA() {
            const exportSection = document.getElementById('exportSection');
            const exportData = document.getElementById('exportData');

            const exportObject = {
                timestamp: new Date().toISOString(),
                totalQA: qaData.length,
                editedCount: editedAnswers.size,
                data: qaData
            };

            exportData.value = JSON.stringify(exportObject, null, 2);
            exportSection.classList.remove('hidden');
        }

        function copyToClipboard() {
            const exportData = document.getElementById('exportData');
            exportData.select();
            document.execCommand('copy');
            alert('Data copied to clipboard!');
        }

        function downloadJSON() {
            const exportData = document.getElementById('exportData').value;
            const blob = new Blob([exportData], { type: 'application/json' });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = `qa-data-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // Initialize when page loads
        window.onload = function() {
            initializeQA();
        };
    </script>
</body>
</html>
