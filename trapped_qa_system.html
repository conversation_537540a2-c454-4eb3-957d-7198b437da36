<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trapped Escape Room - Q&A System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #8e44ad, #9b59b6);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .controls {
            padding: 20px 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            align-items: center;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #8e44ad;
            color: white;
        }
        
        .btn-primary:hover {
            background: #7d3c98;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #1e7e34;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-warning:hover {
            background: #e0a800;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .main-content {
            padding: 30px;
        }
        
        .qa-item {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            margin-bottom: 20px;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .qa-item:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .qa-header {
            background: #e9ecef;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
        }
        
        .qa-header:hover {
            background: #dee2e6;
        }
        
        .question {
            font-weight: 600;
            color: #495057;
            flex: 1;
        }
        
        .qa-controls {
            display: flex;
            gap: 10px;
        }
        
        .btn-sm {
            padding: 5px 10px;
            font-size: 0.875em;
        }
        
        .qa-content {
            padding: 20px;
            display: none;
        }
        
        .qa-content.active {
            display: block;
        }
        
        .answer-display {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 15px;
            white-space: pre-wrap;
            line-height: 1.6;
        }
        
        .answer-edit {
            width: 100%;
            min-height: 150px;
            padding: 15px;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            font-family: inherit;
            font-size: 14px;
            line-height: 1.6;
            resize: vertical;
        }
        
        .edit-controls {
            margin-top: 10px;
            display: flex;
            gap: 10px;
        }
        
        .category-filter {
            margin-bottom: 20px;
        }
        
        .category-filter select {
            padding: 8px 12px;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .stats {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: flex;
            gap: 30px;
            flex-wrap: wrap;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 1.5em;
            font-weight: bold;
            color: #8e44ad;
        }
        
        .stat-label {
            font-size: 0.875em;
            color: #666;
        }
        
        .search-box {
            width: 300px;
            padding: 8px 12px;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .export-section {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
        }
        
        .hidden {
            display: none;
        }
        
        .collapse-icon {
            transition: transform 0.3s ease;
        }
        
        .collapse-icon.rotated {
            transform: rotate(180deg);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏠 Trapped Escape Room Q&A</h1>
            <p>75-minute escape room experiences | Mental puzzles & problem-solving</p>
        </div>
        
        <div class="controls">
            <button class="btn btn-primary" onclick="generateNewQA()">🔄 Generate New Q&A</button>
            <button class="btn btn-success" onclick="addCustomQA()">➕ Add Custom Q&A</button>
            <button class="btn btn-warning" onclick="exportQA()">📤 Export All Q&A</button>
            <input type="text" class="search-box" placeholder="Search questions..." onkeyup="searchQA(this.value)">
            <div class="category-filter">
                <select onchange="filterByCategory(this.value)">
                    <option value="">All Categories</option>
                    <option value="general">General Info</option>
                    <option value="pricing">Pricing</option>
                    <option value="booking">Booking</option>
                    <option value="games">Games</option>
                    <option value="policies">Policies</option>
                </select>
            </div>
        </div>
        
        <div class="main-content">
            <div class="stats">
                <div class="stat-item">
                    <div class="stat-number" id="totalQA">0</div>
                    <div class="stat-label">Total Q&A</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="editedQA">0</div>
                    <div class="stat-label">Edited</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="categoriesCount">0</div>
                    <div class="stat-label">Categories</div>
                </div>
            </div>
            
            <div id="qaContainer">
                <!-- Q&A items will be dynamically generated here -->
            </div>
            
            <div class="export-section hidden" id="exportSection">
                <h4>📋 Export Data</h4>
                <textarea id="exportData" readonly style="width: 100%; height: 200px; margin-top: 10px;"></textarea>
                <div style="margin-top: 10px;">
                    <button class="btn btn-primary" onclick="copyToClipboard()">📋 Copy to Clipboard</button>
                    <button class="btn btn-secondary" onclick="downloadJSON()">💾 Download JSON</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let qaData = [];
        let editedAnswers = new Set();
        
        // Trapped Escape Room specific Q&A
        const trappedQAData = [
            {
                id: 1,
                category: 'general',
                question: 'What is Trapped Escape Room?',
                answer: `🏠 **TRAPPED ESCAPE ROOM**

We offer traditional escape room experiences where teams work together to solve puzzles, find clues, and escape themed rooms within 75 minutes.

**KEY FEATURES:**
- 75-minute escape room experiences
- 7 uniquely themed rooms
- Mental puzzles and problem-solving focus
- Perfect for mystery and investigation lovers
- Live actor involvement in some rooms

**LOCATION:**
8 Grange Road, #02-08 
Cathay Cineleisure Orchard
Singapore 239695

**OPERATING HOURS:**
Daily from 11am - 10pm`
            },
            {
                id: 2,
                category: 'games',
                question: 'What escape room games do you have?',
                answer: `🎬 **THE CURSED VIDEO TAPE** - Thriller
Suspenseful and gritty with live actor involvement. Perfect for thrill-seekers!

🔬 **THE STRANGEST THINGS** - Science Fiction
Mystery-focused sci-fi adventure. Great for puzzle enthusiasts!

🏛️ **THE GREAT ESCAPE** - Prison Theme
Witty and fun with brief live actor scenes. Classic escape experience!

👻 **HOSTEL WARLOCK** - Supernatural
Investigative mystery with supernatural elements. Atmospheric and engaging!

🏠 **THE ATTIC** - Kidnapped Theme
Thriller and mystery with suspenseful elements. Heart-pumping adventure!

🏥 **THE ASYLUM** - Investigative
Mystery and suspense-focused. Perfect for detective enthusiasts!

🔮 **THE ALCHEMIST'S CABINET** - Magic Theme
NO scary elements! Family-friendly magical adventure perfect for those who dislike horror.

**Duration:** 75 minutes each
**Focus:** Mental challenges, puzzles, and teamwork`
            }
        ];

        // Additional Trapped-specific Q&A
        const moreTrappedQA = [
            {
                id: 3,
                category: 'pricing',
                question: 'What are your escape room pricing and packages?',
                answer: `🏠 **TRAPPED ESCAPE ROOM PRICING (75 mins each)**

**OFF-PEAK (Mon-Fri before 6pm):**
• 1-19 people: $28.90 per game per person
• 20-39 people: $27.90 per game per person
• 40+ people: $26.90 per game per person

**PEAK (Weekdays after 6pm, weekends & holidays):**
• 1-19 people: $34.90 per game per person
• 20-39 people: $33.90 per game per person
• 40+ people: $32.90 per game per person

**EARLY BIRD SPECIAL:**
Additional $3 discount per game per person if deposit paid 3 months in advance (minimum 15 people)

*All prices exclude GST*

📦 **PACKAGES AVAILABLE:**
• Package A: 1 escape game per person
• Package B: 2 escape games per person
• Package C: 3 escape games per person

🎯 **MYSTERY HUNT ADD-ON:** $20 per person (any package)`
            },
            {
                id: 4,
                category: 'pricing',
                question: 'Do you offer group discounts for escape rooms?',
                answer: `Yes! We offer attractive group discounts for Trapped Escape Room:

🏠 **TRAPPED ESCAPE ROOM GROUP DISCOUNTS:**
- 1-19 pax: Standard rate
- 20-39 pax: $1 discount per game per person
- 40+ pax: $2 discount per game per person

💰 **EARLY BIRD SAVINGS:**
- Additional $3 off per game per person
- Must pay deposit 3 months in advance
- Minimum 15 people required
- Combines with group discounts!

**EXAMPLE SAVINGS (30 people, 1 game each, off-peak):**
Regular rate: 30 × $28.90 = $867
Group discount: 30 × $27.90 = $837
Early bird: 30 × $24.90 = $747
**Total savings: $120!**

*All prices exclude GST*`
            },
            {
                id: 5,
                category: 'booking',
                question: 'How do I book escape room sessions?',
                answer: `📋 **ESCAPE ROOM BOOKING PROCESS:**

1. **Choose your games:** Select from our 7 themed rooms
2. **Select participants:** How many people in your group
3. **Pick your package:** 1, 2, or 3 games per person
4. **Choose date & time:** We'll confirm availability
5. **Add Mystery Hunt:** Optional $20/person add-on
6. **Receive quote:** Total pricing with discounts
7. **Pay deposit:** 50% to secure your slot
8. **Final payment:** Balance on event day

💳 **PAYMENT METHODS:**
- Bank transfer, Credit card (Visa, Master, Amex)
- PayNow UEN, Cash (on event day)
- VENDORS.GOV.SG / GEBIZ (for government bookings)

⏰ **IMPORTANT TIMING:**
- Confirm booking within 3 working days
- Bookings are first-come, first-served
- We recommend booking 2-3 weeks in advance

**INFORMATION WE NEED:**
- Contact person name & mobile
- Organization name & billing address
- 1st and 2nd contact persons`
            },
            {
                id: 6,
                category: 'policies',
                question: 'What is your cancellation and rescheduling policy?',
                answer: `❌ **CANCELLATION POLICY:**
- All deposits are strictly non-refundable
- No exceptions for any reason (weather, illness, etc.)
- This policy helps us maintain fair pricing

🔄 **RESCHEDULING OPTIONS:**
- **One-time rescheduling** available (subject to availability)
- Must notify us at least 1 week in advance
- Only one rescheduling permitted per booking
- Same package and participant count

📅 **HOW TO RESCHEDULE:**
1. Contact us at least 1 week before your event
2. Provide 2-3 alternative dates/times
3. We'll check availability and confirm
4. No additional charges if within same pricing tier

⚠️ **RESTRICTIONS:**
- Less than 1 week notice = no rescheduling allowed
- Second rescheduling attempt = deposit forfeiture
- Peak to off-peak moves may require price adjustment`
            },
            {
                id: 7,
                category: 'general',
                question: 'Are there age restrictions or accessibility concerns?',
                answer: `👥 **AGE REQUIREMENTS:**
- No specific age restrictions
- Children should be accompanied by adults
- Games are designed for teens and adults
- Mental challenges suitable for all ages

♿ **ACCESSIBILITY:**
- Unfortunately, our venue is not wheelchair accessible
- Escape rooms focus on mental challenges rather than physical activity
- Suitable for pregnant guests (mental puzzles only)
- Perfect alternative for those with mobility concerns

🧠 **GAME DIFFICULTY:**
- Puzzles range from beginner to advanced
- Teams can work together regardless of individual skill levels
- Hints available if teams get stuck
- Success depends on teamwork and communication`
            },
            {
                id: 8,
                category: 'policies',
                question: 'Can I change the number of participants after booking?',
                answer: `📊 **GROUP SIZE POLICY FOR ESCAPE ROOMS:**

**TIMING:**
- Changes allowed up to 1 week before event
- Numbers locked 1 week before event date
- No further changes after lock-in period

💰 **BILLING ADJUSTMENTS:**
- Increase in participants = additional charges
- Decrease in participants = no refund
- No-shows still charged full amount
- Additional participants on event day = revised invoice

📋 **EXAMPLES:**
- Booked for 20, only 15 show up = pay for 20
- Booked for 20, 25 show up = pay for 25
- Reduce from 30 to 25 (1+ week notice) = pay for 25

⚠️ **IMPORTANT REMINDERS:**
- Group discounts recalculated based on final numbers
- Room capacity limits apply per game
- Last-minute additions may not be accommodated

💡 **OUR ADVICE:**
- Book for your maximum expected attendance
- Confirm final numbers 1 week before
- Have a buffer for last-minute additions`
            }
        ];

        // Initialize the system
        function initializeQA() {
            qaData = [...trappedQAData, ...moreTrappedQA];
            renderQA();
            updateStats();
        }

        function renderQA(filteredData = null) {
            const container = document.getElementById('qaContainer');
            const dataToRender = filteredData || qaData;

            container.innerHTML = dataToRender.map(qa => `
                <div class="qa-item" data-category="${qa.category}" data-id="${qa.id}">
                    <div class="qa-header" onclick="toggleQA(${qa.id})">
                        <div class="question">${qa.question}</div>
                        <div class="qa-controls">
                            <span class="collapse-icon" id="icon-${qa.id}">▼</span>
                            <button class="btn btn-sm btn-warning" onclick="event.stopPropagation(); editAnswer(${qa.id})">✏️ Edit</button>
                            <button class="btn btn-sm btn-danger" onclick="event.stopPropagation(); deleteQA(${qa.id})">🗑️ Delete</button>
                        </div>
                    </div>
                    <div class="qa-content" id="content-${qa.id}">
                        <div class="answer-display" id="answer-${qa.id}">${qa.answer}</div>
                        <div class="hidden" id="edit-${qa.id}">
                            <textarea class="answer-edit" id="textarea-${qa.id}">${qa.answer}</textarea>
                            <div class="edit-controls">
                                <button class="btn btn-success btn-sm" onclick="saveAnswer(${qa.id})">💾 Save</button>
                                <button class="btn btn-secondary btn-sm" onclick="cancelEdit(${qa.id})">❌ Cancel</button>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        function toggleQA(id) {
            const content = document.getElementById(`content-${id}`);
            const icon = document.getElementById(`icon-${id}`);

            if (content.classList.contains('active')) {
                content.classList.remove('active');
                icon.textContent = '▼';
                icon.classList.remove('rotated');
            } else {
                content.classList.add('active');
                icon.textContent = '▲';
                icon.classList.add('rotated');
            }
        }

        function editAnswer(id) {
            const answerDiv = document.getElementById(`answer-${id}`);
            const editDiv = document.getElementById(`edit-${id}`);

            answerDiv.classList.add('hidden');
            editDiv.classList.remove('hidden');
        }

        function saveAnswer(id) {
            const textarea = document.getElementById(`textarea-${id}`);
            const newAnswer = textarea.value;

            // Update the data
            const qaItem = qaData.find(qa => qa.id === id);
            if (qaItem) {
                qaItem.answer = newAnswer;
                editedAnswers.add(id);
            }

            // Update the display
            document.getElementById(`answer-${id}`).textContent = newAnswer;
            cancelEdit(id);
            updateStats();
        }

        function cancelEdit(id) {
            const answerDiv = document.getElementById(`answer-${id}`);
            const editDiv = document.getElementById(`edit-${id}`);

            answerDiv.classList.remove('hidden');
            editDiv.classList.add('hidden');
        }

        function deleteQA(id) {
            if (confirm('Are you sure you want to delete this Q&A?')) {
                qaData = qaData.filter(qa => qa.id !== id);
                editedAnswers.delete(id);
                renderQA();
                updateStats();
            }
        }

        function generateNewQA() {
            const newQuestions = [
                {
                    category: 'general',
                    question: 'What should participants wear for escape rooms?',
                    answer: `👕 **DRESS CODE FOR ESCAPE ROOMS:**
- Comfortable clothing recommended
- Covered footwear mandatory (no sandals, heels, crocs or slippers)
- No special physical requirements (mental challenges only)

📝 **PRE-EVENT REQUIREMENTS:**
- Arrive 10-15 minutes early
- All participants must sign waiver forms
- Organize team groupings before arrival
- Bring positive attitude and teamwork spirit!`
                },
                {
                    category: 'booking',
                    question: 'What information do you need for escape room booking?',
                    answer: `**INFORMATION WE NEED FOR BOOKING:**
- Contact person name & mobile number
- Organization name & billing address
- 1st and 2nd contact persons with mobile numbers
- Number of participants
- Preferred date and time
- Which escape room themes interest you
- Package preference (1, 2, or 3 games per person)
- Any special requirements or accessibility needs`
                },
                {
                    category: 'games',
                    question: 'How difficult are the escape room puzzles?',
                    answer: `🧩 **PUZZLE DIFFICULTY LEVELS:**

**BEGINNER-FRIENDLY:**
- The Alchemist's Cabinet (no scary elements)
- The Great Escape (witty and fun)

**INTERMEDIATE:**
- The Strangest Things (sci-fi mystery)
- The Asylum (investigative focus)

**ADVANCED/THRILLING:**
- The Cursed Video Tape (live actor, intense)
- Hostel Warlock (supernatural elements)
- The Attic (suspenseful thriller)

**SUCCESS TIPS:**
- Teams can work together regardless of individual skill
- Hints available if you get stuck
- Communication and teamwork are key
- 75 minutes is usually sufficient for most teams`
                }
            ];

            const randomQuestion = newQuestions[Math.floor(Math.random() * newQuestions.length)];
            const newId = Math.max(...qaData.map(qa => qa.id)) + 1;

            qaData.push({
                id: newId,
                ...randomQuestion
            });

            renderQA();
            updateStats();
            alert('New escape room Q&A generated! Check the bottom of the list.');
        }

        function addCustomQA() {
            const question = prompt('Enter your escape room question:');
            if (!question) return;

            const answer = prompt('Enter the answer:');
            if (!answer) return;

            const category = prompt('Enter category (general/pricing/booking/games/policies):') || 'general';

            const newId = Math.max(...qaData.map(qa => qa.id)) + 1;

            qaData.push({
                id: newId,
                category: category,
                question: question,
                answer: answer
            });

            renderQA();
            updateStats();
        }

        function searchQA(searchTerm) {
            if (!searchTerm.trim()) {
                renderQA();
                return;
            }

            const filtered = qaData.filter(qa =>
                qa.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
                qa.answer.toLowerCase().includes(searchTerm.toLowerCase())
            );

            renderQA(filtered);
        }

        function filterByCategory(category) {
            if (!category) {
                renderQA();
                return;
            }

            const filtered = qaData.filter(qa => qa.category === category);
            renderQA(filtered);
        }

        function updateStats() {
            document.getElementById('totalQA').textContent = qaData.length;
            document.getElementById('editedQA').textContent = editedAnswers.size;

            const categories = [...new Set(qaData.map(qa => qa.category))];
            document.getElementById('categoriesCount').textContent = categories.length;
        }

        function exportQA() {
            const exportSection = document.getElementById('exportSection');
            const exportData = document.getElementById('exportData');

            const exportObject = {
                outlet: 'Trapped Escape Room',
                timestamp: new Date().toISOString(),
                totalQA: qaData.length,
                editedCount: editedAnswers.size,
                data: qaData
            };

            exportData.value = JSON.stringify(exportObject, null, 2);
            exportSection.classList.remove('hidden');
        }

        function copyToClipboard() {
            const exportData = document.getElementById('exportData');
            exportData.select();
            document.execCommand('copy');
            alert('Trapped Escape Room Q&A data copied to clipboard!');
        }

        function downloadJSON() {
            const exportData = document.getElementById('exportData').value;
            const blob = new Blob([exportData], { type: 'application/json' });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = `trapped-qa-data-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // Initialize when page loads
        window.onload = function() {
            initializeQA();
        };
    </script>
</body>
</html>
